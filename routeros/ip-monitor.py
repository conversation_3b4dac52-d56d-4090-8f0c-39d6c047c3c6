#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Synapse自动部署系统 - RouterOS IP监控脚本
支持RouterOS 7.x REST API和动态IP监控
"""

import os
import sys
import time
import json
import logging
import requests
import configparser
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
project_root = script_dir.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y年%m月%d日 %H:%M:%S',
    handlers=[
        logging.FileHandler(script_dir / 'ip-monitor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class RouterOSMonitor:
    """RouterOS IP监控类"""
    
    def __init__(self, config_file=None):
        """初始化监控器"""
        # 优先使用internal目录内的配置文件
        if config_file is None:
            internal_config = script_dir.parent / 'config.env'
            if internal_config.exists():
                self.config_file = internal_config
            else:
                self.config_file = project_root / 'config.env'
        else:
            self.config_file = config_file
        self.config = {}
        self.current_ip = None
        self.last_update = None
        
        # 加载配置
        self.load_config()
        
        # 验证配置
        if not self.validate_config():
            raise ValueError("RouterOS配置无效")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if not self.config_file.exists():
                logger.error(f"配置文件不存在: {self.config_file}")
                return False
            
            # 读取环境变量格式的配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        # 移除引号
                        value = value.strip('"\'')
                        self.config[key] = value
            
            logger.info("配置文件加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def validate_config(self):
        """验证配置"""
        required_keys = [
            'ENABLE_ROUTEROS',
            'ROUTEROS_HOST',
            'ROUTEROS_USERNAME',
            'ROUTEROS_PASSWORD',
            'ROUTEROS_WAN_INTERFACE'
        ]
        
        # 检查是否启用RouterOS
        if self.config.get('ENABLE_ROUTEROS', 'false').lower() != 'true':
            logger.info("RouterOS集成未启用")
            return False
        
        # 检查必需配置
        for key in required_keys:
            if not self.config.get(key):
                logger.error(f"缺少必需配置: {key}")
                return False
        
        return True
    
    def get_external_ip(self):
        """获取外部IP地址"""
        ip_services = [
            'https://ifconfig.me/ip',
            'https://api.ipify.org',
            'https://checkip.amazonaws.com',
            'https://icanhazip.com'
        ]
        
        for service in ip_services:
            try:
                response = requests.get(service, timeout=10)
                if response.status_code == 200:
                    ip = response.text.strip()
                    logger.debug(f"从 {service} 获取到IP: {ip}")
                    return ip
            except Exception as e:
                logger.warning(f"从 {service} 获取IP失败: {e}")
                continue
        
        logger.error("无法获取外部IP地址")
        return None
    
    def connect_routeros(self):
        """连接到RouterOS设备"""
        try:
            # 使用REST API连接（RouterOS 7.x）
            host = self.config['ROUTEROS_HOST']
            username = self.config['ROUTEROS_USERNAME']
            password = self.config['ROUTEROS_PASSWORD']
            port = self.config.get('ROUTEROS_PORT', '8728')
            
            # 构建API URL
            if port == '80':
                api_url = f"http://{host}/rest"
            elif port == '443':
                api_url = f"https://{host}/rest"
            else:
                api_url = f"http://{host}:{port}/rest"
            
            # 测试连接
            auth = (username, password)
            response = requests.get(f"{api_url}/system/identity", 
                                  auth=auth, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"成功连接到RouterOS: {host}")
                return api_url, auth
            else:
                logger.error(f"RouterOS连接失败: HTTP {response.status_code}")
                return None, None
                
        except Exception as e:
            logger.error(f"连接RouterOS失败: {e}")
            return None, None
    
    def update_routeros_ip(self, new_ip):
        """更新RouterOS中的IP配置"""
        try:
            api_url, auth = self.connect_routeros()
            if not api_url:
                return False

            wan_interface = self.config.get('ROUTEROS_WAN_INTERFACE', 'WAN')
            logger.info(f"更新RouterOS IP配置: {new_ip} (接口: {wan_interface})")

            # 获取WAN接口信息
            interface_response = requests.get(
                f"{api_url}/interface",
                auth=auth,
                params={'name': wan_interface}
            )

            if interface_response.status_code == 200:
                logger.info(f"成功获取{wan_interface}接口信息")
                # 这里可以根据具体需求实现IP更新逻辑
                # 例如：更新NAT规则、防火墙规则等
            else:
                logger.warning(f"无法获取{wan_interface}接口信息")

            return True
            
        except Exception as e:
            logger.error(f"更新RouterOS IP失败: {e}")
            return False
    
    def update_dns_records(self, new_ip):
        """更新DNS记录"""
        try:
            # 如果配置了Cloudflare API，更新DNS记录
            cf_token = self.config.get('CLOUDFLARE_API_TOKEN')
            if not cf_token:
                logger.warning("未配置Cloudflare API Token，跳过DNS更新")
                return True
            
            domain_name = self.config.get('DOMAIN_NAME')
            if not domain_name:
                logger.warning("未配置域名，跳过DNS更新")
                return True
            
            # 更新A记录
            headers = {
                'Authorization': f'Bearer {cf_token}',
                'Content-Type': 'application/json'
            }
            
            # 获取Zone ID
            zone_response = requests.get(
                f'https://api.cloudflare.com/client/v4/zones?name={domain_name}',
                headers=headers
            )
            
            if zone_response.status_code != 200:
                logger.error("获取Cloudflare Zone信息失败")
                return False
            
            zones = zone_response.json()['result']
            if not zones:
                logger.error(f"未找到域名 {domain_name} 的Zone")
                return False
            
            zone_id = zones[0]['id']
            
            # 更新子域名记录
            subdomains = [domain_name, f"matrix.{domain_name}", f"turn.{domain_name}"]
            
            for subdomain in subdomains:
                # 获取现有记录
                records_response = requests.get(
                    f'https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records?name={subdomain}&type=A',
                    headers=headers
                )
                
                if records_response.status_code == 200:
                    records = records_response.json()['result']
                    
                    if records:
                        # 更新现有记录
                        record_id = records[0]['id']
                        update_data = {
                            'type': 'A',
                            'name': subdomain,
                            'content': new_ip,
                            'ttl': 300
                        }
                        
                        update_response = requests.put(
                            f'https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records/{record_id}',
                            headers=headers,
                            json=update_data
                        )
                        
                        if update_response.status_code == 200:
                            logger.info(f"DNS记录更新成功: {subdomain} -> {new_ip}")
                        else:
                            logger.error(f"DNS记录更新失败: {subdomain}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新DNS记录失败: {e}")
            return False
    
    def save_ip_history(self, ip):
        """保存IP变更历史"""
        try:
            history_file = script_dir / 'ip_history.json'
            history = []
            
            # 读取现有历史
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 添加新记录
            history.append({
                'ip': ip,
                'timestamp': datetime.now().isoformat(),
                'date': datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
            })
            
            # 保留最近100条记录
            history = history[-100:]
            
            # 保存历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"IP历史记录已保存: {ip}")
            
        except Exception as e:
            logger.error(f"保存IP历史失败: {e}")
    
    def monitor_ip(self):
        """监控IP变化"""
        logger.info("开始IP监控...")
        
        check_interval = int(self.config.get('IP_CHECK_INTERVAL', '300'))
        
        while True:
            try:
                # 获取当前外部IP
                new_ip = self.get_external_ip()
                
                if new_ip and new_ip != self.current_ip:
                    logger.info(f"检测到IP变化: {self.current_ip} -> {new_ip}")
                    
                    # 更新RouterOS配置
                    if self.update_routeros_ip(new_ip):
                        logger.info("RouterOS配置更新成功")
                    
                    # 更新DNS记录
                    if self.update_dns_records(new_ip):
                        logger.info("DNS记录更新成功")
                    
                    # 保存历史记录
                    self.save_ip_history(new_ip)
                    
                    # 更新当前IP
                    self.current_ip = new_ip
                    self.last_update = datetime.now()
                    
                elif new_ip:
                    logger.debug(f"IP未变化: {new_ip}")
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                time.sleep(60)  # 错误后等待1分钟再重试

def main():
    """主函数"""
    try:
        # 创建监控器
        monitor = RouterOSMonitor()
        
        # 开始监控
        monitor.monitor_ip()
        
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
