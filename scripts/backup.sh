#!/bin/bash

# Synapse自动部署系统 - 备份脚本
# 实现系统备份和恢复功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTERNAL_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 配置文件路径（优先使用internal目录内的配置）
if [[ -f "$INTERNAL_DIR/config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/config.env"
elif [[ -f "$INTERNAL_DIR/../config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/../config.env"
else
    CONFIG_FILE="$INTERNAL_DIR/config.env"
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# 加载配置
load_config() {
    # 自动创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件不存在，正在创建默认配置文件..."

        if [[ -f "$INTERNAL_DIR/config-example.env" ]]; then
            cp "$INTERNAL_DIR/config-example.env" "$CONFIG_FILE"
            chmod 600 "$CONFIG_FILE"
            log_info "已创建配置文件: $CONFIG_FILE"
        fi
    fi

    if [[ -f "$CONFIG_FILE" ]]; then
        # 安全地加载配置文件
        while IFS='=' read -r key value; do
            if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
                value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
                export "$key"="$value"
            fi
        done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')
    fi
    
    # 设置默认值
    BACKUP_PATH="${BACKUP_PATH:-$INTERNAL_DIR/backup}"
    BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
    DOMAIN_NAME="${DOMAIN_NAME:-example.com}"
}

# 创建备份目录
create_backup_dir() {
    local timestamp="$1"
    local backup_dir="$BACKUP_PATH/$timestamp"
    
    mkdir -p "$backup_dir"
    echo "$backup_dir"
}

# 备份配置文件
backup_configs() {
    local backup_dir="$1"
    
    log_step "备份配置文件"
    
    local config_dir="$backup_dir/configs"
    mkdir -p "$config_dir"
    
    # 备份主配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        cp "$CONFIG_FILE" "$config_dir/"
        log_info "已备份: config.env"
    fi
    
    # 备份Docker配置
    if [[ -f "$INTERNAL_DIR/docker-compose.yml" ]]; then
        cp "$INTERNAL_DIR/docker-compose.yml" "$config_dir/"
        log_info "已备份: docker-compose.yml"
    fi
    
    # 备份服务配置文件
    local service_configs=(
        "homeserver.yaml"
        "nginx.conf"
        "coturn.conf"
        "log.config"
    )
    
    for config in "${service_configs[@]}"; do
        if [[ -f "$INTERNAL_DIR/$config" ]]; then
            cp "$INTERNAL_DIR/$config" "$config_dir/"
            log_info "已备份: $config"
        fi
    done
    
    # 备份配置模板
    if [[ -d "$INTERNAL_DIR/configs" ]]; then
        cp -r "$INTERNAL_DIR/configs" "$config_dir/templates"
        log_info "已备份: 配置模板"
    fi
}

# 备份数据库
backup_database() {
    local backup_dir="$1"
    
    log_step "备份数据库"
    
    local db_dir="$backup_dir/database"
    mkdir -p "$db_dir"
    
    # 检查Synapse容器是否运行
    if ! docker compose -f "$INTERNAL_DIR/docker-compose.yml" ps synapse | grep -q "Up"; then
        log_warn "Synapse容器未运行，跳过数据库备份"
        return 0
    fi
    
    # 备份SQLite数据库
    if docker compose -f "$INTERNAL_DIR/docker-compose.yml" exec synapse test -f /data/homeserver.db; then
        log_info "备份SQLite数据库..."
        docker compose -f "$INTERNAL_DIR/docker-compose.yml" exec synapse \
            sqlite3 /data/homeserver.db ".backup /data/homeserver_backup.db"
        
        docker compose -f "$INTERNAL_DIR/docker-compose.yml" exec synapse \
            cp /data/homeserver_backup.db /data/homeserver_backup_$(date +%Y%m%d_%H%M%S).db
        
        # 复制到备份目录
        docker cp "$(docker compose -f "$INTERNAL_DIR/docker-compose.yml" ps -q synapse):/data/homeserver_backup_$(date +%Y%m%d_%H%M%S).db" \
            "$db_dir/homeserver.db"
        
        log_info "SQLite数据库备份完成"
    else
        log_warn "未找到SQLite数据库文件"
    fi
}

# 备份媒体文件
backup_media() {
    local backup_dir="$1"
    
    log_step "备份媒体文件"
    
    local media_dir="$backup_dir/media"
    mkdir -p "$media_dir"
    
    # 备份媒体存储
    if [[ -d "$INTERNAL_DIR/media_store" ]]; then
        log_info "备份媒体存储..."
        tar -czf "$media_dir/media_store.tar.gz" -C "$INTERNAL_DIR" media_store
        log_info "媒体存储备份完成"
    fi
    
    # 备份上传文件
    if [[ -d "$INTERNAL_DIR/uploads" ]]; then
        log_info "备份上传文件..."
        tar -czf "$media_dir/uploads.tar.gz" -C "$INTERNAL_DIR" uploads
        log_info "上传文件备份完成"
    fi
}

# 备份SSL证书
backup_certificates() {
    local backup_dir="$1"
    
    log_step "备份SSL证书"
    
    local cert_dir="$backup_dir/certificates"
    mkdir -p "$cert_dir"
    
    # 备份SSL目录
    if [[ -d "$INTERNAL_DIR/ssl" ]]; then
        cp -r "$INTERNAL_DIR/ssl" "$cert_dir/"
        log_info "已备份SSL证书目录"
    fi
    
    # 备份acme.sh证书（如果存在）
    local acme_paths=(
        "$HOME/.acme.sh"
        "/root/.acme.sh"
    )
    
    for acme_path in "${acme_paths[@]}"; do
        if [[ -d "$acme_path" ]]; then
            log_info "备份acme.sh证书: $acme_path"
            tar -czf "$cert_dir/acme_sh_$(basename "$acme_path").tar.gz" -C "$(dirname "$acme_path")" "$(basename "$acme_path")"
            break
        fi
    done
}

# 备份日志文件
backup_logs() {
    local backup_dir="$1"
    
    log_step "备份日志文件"
    
    local log_dir="$backup_dir/logs"
    mkdir -p "$log_dir"
    
    # 备份应用日志
    if [[ -d "$INTERNAL_DIR/logs" ]]; then
        cp -r "$INTERNAL_DIR/logs" "$log_dir/app_logs"
        log_info "已备份应用日志"
    fi
    
    # 备份Docker日志
    log_info "导出Docker容器日志..."
    local containers=("synapse" "nginx" "coturn")
    
    for container in "${containers[@]}"; do
        if docker compose -f "$INTERNAL_DIR/docker-compose.yml" ps "$container" | grep -q "Up"; then
            docker compose -f "$INTERNAL_DIR/docker-compose.yml" logs "$container" > "$log_dir/${container}_docker.log" 2>&1
            log_info "已导出 $container 容器日志"
        fi
    done
}

# 创建备份信息文件
create_backup_info() {
    local backup_dir="$1"
    local timestamp="$2"
    
    local info_file="$backup_dir/backup_info.txt"
    
    cat > "$info_file" << EOF
Synapse自动部署系统 - 备份信息
=====================================

备份时间: $(date '+%Y年%m月%d日 %H:%M:%S')
备份标识: $timestamp
系统信息: $(uname -a)
域名: $DOMAIN_NAME

备份内容:
- 配置文件
- 数据库文件
- 媒体文件
- SSL证书
- 日志文件

Docker信息:
$(docker --version)
$(docker compose version)

容器状态:
$(docker compose -f "$INTERNAL_DIR/docker-compose.yml" ps 2>/dev/null || echo "Docker服务未运行")

备份大小: $(du -sh "$backup_dir" | cut -f1)
EOF

    log_info "备份信息文件已创建"
}

# 压缩备份
compress_backup() {
    local backup_dir="$1"
    local timestamp="$2"
    
    log_step "压缩备份文件"
    
    local archive_name="synapse_backup_${timestamp}.tar.gz"
    local archive_path="$BACKUP_PATH/$archive_name"
    
    cd "$BACKUP_PATH"
    tar -czf "$archive_name" "$timestamp"
    
    if [[ -f "$archive_path" ]]; then
        local archive_size
        archive_size=$(du -sh "$archive_path" | cut -f1)
        log_info "备份压缩完成: $archive_name ($archive_size)"
        
        # 删除原始备份目录
        rm -rf "$backup_dir"
        log_info "已清理临时备份目录"
        
        echo "$archive_path"
    else
        log_error "备份压缩失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_step "清理旧备份"
    
    if [[ ! -d "$BACKUP_PATH" ]]; then
        return 0
    fi
    
    # 删除超过保留期的备份文件
    find "$BACKUP_PATH" -name "synapse_backup_*.tar.gz" -type f -mtime +$BACKUP_RETENTION_DAYS -delete
    
    local remaining_count
    remaining_count=$(find "$BACKUP_PATH" -name "synapse_backup_*.tar.gz" -type f | wc -l)
    
    log_info "保留备份数量: $remaining_count"
    log_info "备份保留期: $BACKUP_RETENTION_DAYS 天"
}

# 执行完整备份
full_backup() {
    local timestamp
    timestamp=$(date '+%Y%m%d_%H%M%S')
    
    log_info "开始完整备份: $timestamp"
    
    # 创建备份目录
    local backup_dir
    backup_dir=$(create_backup_dir "$timestamp")
    
    # 执行各项备份
    backup_configs "$backup_dir"
    backup_database "$backup_dir"
    backup_media "$backup_dir"
    backup_certificates "$backup_dir"
    backup_logs "$backup_dir"
    
    # 创建备份信息
    create_backup_info "$backup_dir" "$timestamp"
    
    # 压缩备份
    local archive_path
    archive_path=$(compress_backup "$backup_dir" "$timestamp")
    
    # 清理旧备份
    cleanup_old_backups
    
    log_info "完整备份完成: $archive_path"
    return 0
}

# 列出备份
list_backups() {
    log_step "备份列表"
    
    if [[ ! -d "$BACKUP_PATH" ]]; then
        log_warn "备份目录不存在: $BACKUP_PATH"
        return 0
    fi
    
    local backups
    backups=$(find "$BACKUP_PATH" -name "synapse_backup_*.tar.gz" -type f | sort -r)
    
    if [[ -z "$backups" ]]; then
        log_warn "未找到备份文件"
        return 0
    fi
    
    echo -e "${CYAN}可用备份:${NC}"
    echo
    
    local count=1
    while IFS= read -r backup; do
        local filename
        filename=$(basename "$backup")
        local size
        size=$(du -sh "$backup" | cut -f1)
        local date
        date=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
        
        echo -e "${GREEN}[$count]${NC} $filename"
        echo -e "    大小: $size"
        echo -e "    时间: $date"
        echo
        
        ((count++))
    done <<< "$backups"
}

# 显示帮助
show_help() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 备份管理工具${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}使用方法:${NC}"
    echo -e "  $0 [命令]"
    echo
    echo -e "${GREEN}可用命令:${NC}"
    echo -e "  ${GREEN}backup${NC}          执行完整备份"
    echo -e "  ${GREEN}list${NC}            列出所有备份"
    echo -e "  ${GREEN}cleanup${NC}         清理旧备份"
    echo -e "  ${GREEN}help${NC}            显示此帮助信息"
    echo
    echo -e "${YELLOW}配置选项:${NC}"
    echo -e "  BACKUP_PATH: 备份存储路径 (默认: ./backup)"
    echo -e "  BACKUP_RETENTION_DAYS: 备份保留天数 (默认: 30)"
    echo
}

# 主函数
main() {
    # 加载配置
    load_config
    
    case "${1:-help}" in
        "backup")
            full_backup
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
