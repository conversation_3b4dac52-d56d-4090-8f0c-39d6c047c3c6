#!/bin/bash

# Synapse自动部署系统 - 独立部署包验证脚本
# 验证部署包的完整性和独立性

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTERNAL_DIR="$(cd "$SCRIPT_DIR/../internal" && pwd)"

# 验证结果
CHECKS_PASSED=0
CHECKS_FAILED=0

# 日志函数
log_pass() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((CHECKS_PASSED++))
}

log_fail() {
    echo -e "${RED}[✗]${NC} $1"
    ((CHECKS_FAILED++))
}

log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}  独立部署包验证${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
}

# 检查必需文件
check_required_files() {
    echo -e "${BLUE}检查必需文件...${NC}"
    
    local required_files=(
        "../config-example.env"
        "../install.sh"
        "../scripts/certificate-manager.sh"
        "../scripts/backup.sh"
        "../scripts/health-check.sh"
        "../scripts/user-manager.sh"
        "../scripts/config-check.sh"
        "../scripts/verify-package.sh"
        "../routeros/ip-monitor.py"
        "../routeros/requirements.txt"
        "docker-compose.yml"
        "deploy.sh"
        "configs/homeserver.yaml.template"
        "configs/nginx.conf.template"
        "configs/coturn.conf.template"
        "configs/log.config.template"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "$INTERNAL_DIR/$file" ]]; then
            log_pass "文件存在: $file"
        else
            log_fail "文件缺失: $file"
        fi
    done
}

# 检查脚本权限
check_script_permissions() {
    echo -e "\n${BLUE}检查脚本权限...${NC}"
    
    local scripts=(
        "deploy.sh"
        "../install.sh"
        "../scripts/config-check.sh"
        "../scripts/verify-package.sh"
        "../scripts/certificate-manager.sh"
        "../scripts/backup.sh"
        "../scripts/health-check.sh"
        "../scripts/user-manager.sh"
        "../routeros/ip-monitor.py"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$INTERNAL_DIR/$script" ]]; then
            if [[ -x "$INTERNAL_DIR/$script" ]]; then
                log_pass "脚本可执行: $script"
            else
                log_fail "脚本不可执行: $script"
            fi
        fi
    done
}

# 检查脚本语法
check_script_syntax() {
    echo -e "\n${BLUE}检查脚本语法...${NC}"
    
    # 检查Bash脚本
    local bash_scripts=(
        "deploy.sh"
        "../install.sh"
        "../scripts/config-check.sh"
        "../scripts/verify-package.sh"
        "../scripts/certificate-manager.sh"
        "../scripts/backup.sh"
        "../scripts/health-check.sh"
        "../scripts/user-manager.sh"
    )
    
    for script in "${bash_scripts[@]}"; do
        if [[ -f "$INTERNAL_DIR/$script" ]]; then
            if bash -n "$INTERNAL_DIR/$script" 2>/dev/null; then
                log_pass "Bash语法正确: $script"
            else
                log_fail "Bash语法错误: $script"
            fi
        fi
    done
    
    # 检查Python脚本
    if [[ -f "$INTERNAL_DIR/../routeros/ip-monitor.py" ]]; then
        if python3 -m py_compile "$INTERNAL_DIR/../routeros/ip-monitor.py" 2>/dev/null; then
            log_pass "Python语法正确: routeros/ip-monitor.py"
        else
            log_fail "Python语法错误: routeros/ip-monitor.py"
        fi
    fi
}

# 检查Docker配置
check_docker_config() {
    echo -e "\n${BLUE}检查Docker配置...${NC}"
    
    if [[ -f "$INTERNAL_DIR/docker-compose.yml" ]]; then
        # 检查Docker Compose语法
        cd "$INTERNAL_DIR"
        if command -v docker >/dev/null 2>&1 && docker compose config >/dev/null 2>&1; then
            log_pass "Docker Compose配置语法正确"
        else
            log_fail "Docker Compose配置语法错误或Docker未安装"
        fi
    fi
}

# 检查独立性
check_independence() {
    echo -e "\n${BLUE}检查包独立性...${NC}"
    
    # 检查是否有对外部文件的硬依赖
    local external_refs
    external_refs=$(find "$INTERNAL_DIR" -name "*.sh" -o -name "*.py" | xargs grep -l "\.\./\.\." 2>/dev/null | grep -v ".gitignore" | wc -l | tr -d ' \n' || echo "0")
    
    if [[ "$external_refs" -eq 0 ]]; then
        log_pass "无外部文件依赖"
    else
        log_fail "发现 $external_refs 个外部文件依赖"
    fi
    
    # 检查配置文件示例
    if [[ -f "$INTERNAL_DIR/../config-example.env" ]]; then
        log_pass "配置示例文件存在"
    else
        log_fail "配置示例文件缺失"
    fi
}

# 生成验证报告
generate_report() {
    echo
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}      验证结果报告${NC}"
    echo -e "${CYAN}================================${NC}"
    
    local total_checks=$((CHECKS_PASSED + CHECKS_FAILED))
    local success_rate=0
    
    if [[ $total_checks -gt 0 ]]; then
        success_rate=$((CHECKS_PASSED * 100 / total_checks))
    fi
    
    echo -e "验证时间: $(date '+%Y年%m月%d日 %H:%M:%S')"
    echo -e "总检查项: $total_checks"
    echo -e "通过检查: ${GREEN}$CHECKS_PASSED${NC}"
    echo -e "失败检查: ${RED}$CHECKS_FAILED${NC}"
    echo -e "成功率: ${success_rate}%"
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo
        echo -e "${GREEN}🎉 独立部署包验证通过！${NC}"
        echo -e "${GREEN}此包可以独立部署到目标服务器。${NC}"
        echo
        echo -e "${CYAN}下一步操作:${NC}"
        echo -e "  1. 复制整个目录到目标服务器"
        echo -e "  2. 配置 config.env 文件"
        echo -e "  3. 运行 ./install.sh 开始部署"
    else
        echo
        echo -e "${RED}❌ 验证失败，请修复上述问题。${NC}"
    fi
    
    echo -e "${CYAN}================================${NC}"
    
    # 返回状态码
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 主函数
main() {
    show_banner
    
    # 执行各项检查
    check_required_files
    check_script_permissions
    check_script_syntax
    check_docker_config
    check_independence
    
    # 生成报告
    generate_report
}

# 执行主函数
main "$@"
