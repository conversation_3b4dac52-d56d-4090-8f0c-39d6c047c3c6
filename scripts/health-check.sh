#!/bin/bash

# Synapse自动部署系统 - 健康检查脚本
# 实现系统健康检查和监控功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTERNAL_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 配置文件路径（优先使用internal目录内的配置）
if [[ -f "$INTERNAL_DIR/config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/config.env"
elif [[ -f "$INTERNAL_DIR/../config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/../config.env"
else
    CONFIG_FILE="$INTERNAL_DIR/config.env"
fi

# 健康检查结果
HEALTH_STATUS="healthy"
ISSUES_FOUND=()

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
    ISSUES_FOUND+=("警告: $1")
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
    HEALTH_STATUS="unhealthy"
    ISSUES_FOUND+=("错误: $1")
}

log_check() {
    echo -e "${BLUE}[检查]${NC} $1"
}

# 加载配置
load_config() {
    # 自动创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件不存在，正在创建默认配置文件..."

        if [[ -f "$INTERNAL_DIR/config-example.env" ]]; then
            cp "$INTERNAL_DIR/config-example.env" "$CONFIG_FILE"
            chmod 600 "$CONFIG_FILE"
            log_info "已创建配置文件: $CONFIG_FILE"
        fi
    fi

    if [[ -f "$CONFIG_FILE" ]]; then
        # 安全地加载配置文件
        while IFS='=' read -r key value; do
            if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
                value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
                export "$key"="$value"
            fi
        done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')
    fi
    
    # 设置默认值
    DOMAIN_NAME="${DOMAIN_NAME:-example.com}"
    MATRIX_DOMAIN="${MATRIX_DOMAIN:-matrix.$DOMAIN_NAME}"
    TURN_DOMAIN="${TURN_DOMAIN:-turn.$DOMAIN_NAME}"
    HTTPS_PORT="${HTTPS_PORT:-443}"
    SYNAPSE_PORT="${SYNAPSE_PORT:-8008}"
}

# 显示健康检查横幅
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 系统健康检查${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "${GREEN}检查时间: $(date '+%Y年%m月%d日 %H:%M:%S')${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
}

# 检查系统资源
check_system_resources() {
    log_check "系统资源状态"
    
    # 检查内存使用（跨平台兼容）
    local mem_usage
    if command -v free >/dev/null 2>&1; then
        # Linux系统
        mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    elif [[ "$(uname)" == "Darwin" ]]; then
        # macOS系统
        local total_mem used_mem
        total_mem=$(sysctl -n hw.memsize)
        used_mem=$(vm_stat | grep "Pages active" | awk '{print $3}' | sed 's/\.//')
        used_mem=$((used_mem * 4096))  # 页面大小通常是4KB
        mem_usage=$(awk -v used="$used_mem" -v total="$total_mem" 'BEGIN {printf "%.0f", used / total * 100}')
    else
        log_warn "无法检测内存使用率（不支持的操作系统）"
        return
    fi

    if [[ "$mem_usage" -gt 90 ]]; then
        log_error "内存使用率过高: ${mem_usage}%"
    elif [[ "$mem_usage" -gt 80 ]]; then
        log_warn "内存使用率较高: ${mem_usage}%"
    else
        log_info "内存使用率正常: ${mem_usage}%"
    fi
    
    # 检查磁盘空间
    local disk_usage
    disk_usage=$(df "$INTERNAL_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ "$disk_usage" -gt 90 ]]; then
        log_error "磁盘空间不足: ${disk_usage}%"
    elif [[ "$disk_usage" -gt 80 ]]; then
        log_warn "磁盘空间较少: ${disk_usage}%"
    else
        log_info "磁盘空间充足: ${disk_usage}%"
    fi
    
    # 检查CPU负载（跨平台兼容）
    local load_avg cpu_cores
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

    if command -v nproc >/dev/null 2>&1; then
        # Linux系统
        cpu_cores=$(nproc)
    elif [[ "$(uname)" == "Darwin" ]]; then
        # macOS系统
        cpu_cores=$(sysctl -n hw.ncpu)
    else
        log_warn "无法检测CPU核心数（不支持的操作系统）"
        return
    fi

    # 使用awk进行浮点数计算，避免bc依赖
    local load_percent
    load_percent=$(awk -v load="$load_avg" -v cores="$cpu_cores" 'BEGIN {printf "%.0f", load / cores * 100}')

    if [[ "$load_percent" -gt 90 ]]; then
        log_error "CPU负载过高: ${load_avg} (${load_percent}%)"
    elif [[ "$load_percent" -gt 70 ]]; then
        log_warn "CPU负载较高: ${load_avg} (${load_percent}%)"
    else
        log_info "CPU负载正常: ${load_avg} (${load_percent}%)"
    fi
}

# 检查Docker服务
check_docker_services() {
    log_check "Docker服务状态"
    
    # 检查Docker是否运行（跨平台兼容）
    if command -v systemctl >/dev/null 2>&1; then
        # Linux系统
        if ! systemctl is-active --quiet docker; then
            log_error "Docker服务未运行"
            return 1
        else
            log_info "Docker服务正常运行"
        fi
    elif command -v docker >/dev/null 2>&1; then
        # 通用检查：尝试运行docker命令
        if docker info >/dev/null 2>&1; then
            log_info "Docker服务正常运行"
        else
            log_error "Docker服务未运行或无权限访问"
            return 1
        fi
    else
        log_error "Docker未安装"
        return 1
    fi
    
    # 检查容器状态
    cd "$INTERNAL_DIR"
    
    local containers=("synapse" "nginx" "coturn")
    for container in "${containers[@]}"; do
        if docker compose ps "$container" | grep -q "Up"; then
            log_info "容器 $container 运行正常"
            
            # 检查容器健康状态
            local health_status
            health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "none")
            
            case "$health_status" in
                "healthy")
                    log_info "容器 $container 健康检查通过"
                    ;;
                "unhealthy")
                    log_error "容器 $container 健康检查失败"
                    ;;
                "starting")
                    log_warn "容器 $container 正在启动中"
                    ;;
                "none")
                    log_info "容器 $container 未配置健康检查"
                    ;;
            esac
        else
            log_error "容器 $container 未运行"
        fi
    done
}

# 检查网络连接
check_network_connectivity() {
    log_check "网络连接状态"
    
    # 检查内部服务连接
    if curl -s -f "http://localhost:$SYNAPSE_PORT/health" >/dev/null; then
        log_info "Synapse内部API连接正常"
    else
        log_error "Synapse内部API连接失败"
    fi
    
    # 检查HTTPS服务
    if curl -s -f -k "https://localhost:$HTTPS_PORT" >/dev/null; then
        log_info "HTTPS服务连接正常"
    else
        log_error "HTTPS服务连接失败"
    fi
    
    # 检查外部网络连接
    if curl -s -f "https://api.github.com" >/dev/null; then
        log_info "外部网络连接正常"
    else
        log_warn "外部网络连接异常"
    fi
    
    # 检查DNS解析
    if nslookup "$MATRIX_DOMAIN" >/dev/null 2>&1; then
        log_info "DNS解析正常: $MATRIX_DOMAIN"
    else
        log_error "DNS解析失败: $MATRIX_DOMAIN"
    fi
}

# 检查SSL证书
check_ssl_certificates() {
    log_check "SSL证书状态"
    
    local domains=("$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN")
    
    for domain in "${domains[@]}"; do
        local cert_path="$INTERNAL_DIR/ssl/$domain/fullchain.pem"
        
        if [[ -f "$cert_path" ]]; then
            # 检查证书有效期
            local expiry_date
            expiry_date=$(openssl x509 -in "$cert_path" -noout -enddate 2>/dev/null | cut -d= -f2)
            
            if [[ -n "$expiry_date" ]]; then
                local expiry_epoch
                if command -v gdate >/dev/null 2>&1; then
                    expiry_epoch=$(gdate -d "$expiry_date" +%s)
                else
                    expiry_epoch=$(date -d "$expiry_date" +%s)
                fi
                
                local current_epoch
                current_epoch=$(date +%s)
                local days_remaining
                days_remaining=$(( (expiry_epoch - current_epoch) / 86400 ))
                
                if [[ "$days_remaining" -lt 7 ]]; then
                    log_error "证书即将过期: $domain ($days_remaining 天)"
                elif [[ "$days_remaining" -lt 30 ]]; then
                    log_warn "证书即将过期: $domain ($days_remaining 天)"
                else
                    log_info "证书有效: $domain ($days_remaining 天)"
                fi
            else
                log_error "无法读取证书有效期: $domain"
            fi
        else
            log_error "证书文件不存在: $domain"
        fi
    done
}

# 检查配置文件
check_configuration_files() {
    log_check "配置文件状态"
    
    local config_files=(
        "$CONFIG_FILE:主配置文件"
        "$INTERNAL_DIR/docker-compose.yml:Docker配置"
        "$INTERNAL_DIR/homeserver.yaml:Synapse配置"
        "$INTERNAL_DIR/nginx.conf:Nginx配置"
        "$INTERNAL_DIR/coturn.conf:Coturn配置"
    )
    
    for config_entry in "${config_files[@]}"; do
        local file_path="${config_entry%:*}"
        local file_desc="${config_entry#*:}"
        
        if [[ -f "$file_path" ]]; then
            log_info "$file_desc 存在"
            
            # 检查文件权限
            local perms
            perms=$(stat -c "%a" "$file_path")
            if [[ "$perms" == "644" ]] || [[ "$perms" == "600" ]]; then
                log_info "$file_desc 权限正确 ($perms)"
            else
                log_warn "$file_desc 权限异常 ($perms)"
            fi
        else
            log_error "$file_desc 不存在: $file_path"
        fi
    done
}

# 检查日志文件
check_log_files() {
    log_check "日志文件状态"
    
    # 检查应用日志
    local log_files=(
        "$INTERNAL_DIR/logs/nginx/access.log"
        "$INTERNAL_DIR/logs/nginx/error.log"
        "$INTERNAL_DIR/data/homeserver.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            local size
            size=$(du -sh "$log_file" | cut -f1)
            log_info "日志文件存在: $(basename "$log_file") ($size)"
            
            # 检查最近的错误
            if grep -q "ERROR\|CRITICAL" "$log_file" 2>/dev/null; then
                local error_count
                error_count=$(grep -c "ERROR\|CRITICAL" "$log_file" | tail -100)
                if [[ "$error_count" -gt 0 ]]; then
                    log_warn "发现 $error_count 个错误在 $(basename "$log_file")"
                fi
            fi
        else
            log_warn "日志文件不存在: $(basename "$log_file")"
        fi
    done
}

# 检查数据库状态
check_database_status() {
    log_check "数据库状态"
    
    # 检查SQLite数据库
    local db_path="$INTERNAL_DIR/data/homeserver.db"
    
    if [[ -f "$db_path" ]]; then
        local db_size
        db_size=$(du -sh "$db_path" | cut -f1)
        log_info "SQLite数据库存在 ($db_size)"
        
        # 检查数据库完整性
        if sqlite3 "$db_path" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_info "数据库完整性检查通过"
        else
            log_error "数据库完整性检查失败"
        fi
    else
        log_error "数据库文件不存在: $db_path"
    fi
}

# 生成健康报告
generate_health_report() {
    echo
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        健康检查报告${NC}"
    echo -e "${CYAN}================================${NC}"
    
    # 显示总体状态
    if [[ "$HEALTH_STATUS" == "healthy" ]]; then
        echo -e "${GREEN}总体状态: 健康 ✓${NC}"
    else
        echo -e "${RED}总体状态: 异常 ✗${NC}"
    fi
    
    echo -e "检查时间: $(date '+%Y年%m月%d日 %H:%M:%S')"
    echo -e "问题数量: ${#ISSUES_FOUND[@]}"
    
    # 显示发现的问题
    if [[ "${#ISSUES_FOUND[@]}" -gt 0 ]]; then
        echo
        echo -e "${YELLOW}发现的问题:${NC}"
        for issue in "${ISSUES_FOUND[@]}"; do
            echo -e "  • $issue"
        done
    fi
    
    echo
    echo -e "${CYAN}建议操作:${NC}"
    if [[ "$HEALTH_STATUS" == "healthy" ]]; then
        echo -e "  • 系统运行正常，继续监控"
    else
        echo -e "  • 检查并解决上述问题"
        echo -e "  • 查看详细日志: docker compose logs"
        echo -e "  • 重启服务: docker compose restart"
    fi
    
    echo -e "${CYAN}================================${NC}"
}

# 主函数
main() {
    show_banner
    load_config
    
    # 执行各项检查
    check_system_resources
    echo
    check_docker_services
    echo
    check_network_connectivity
    echo
    check_ssl_certificates
    echo
    check_configuration_files
    echo
    check_log_files
    echo
    check_database_status
    
    # 生成报告
    generate_health_report
    
    # 返回状态码
    if [[ "$HEALTH_STATUS" == "healthy" ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
