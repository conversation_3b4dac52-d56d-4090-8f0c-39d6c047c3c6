#!/bin/bash

# Synapse自动部署系统 - 智能证书管理脚本
# 支持软链接机制和Let's Encrypt配额保护

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTERNAL_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 配置文件路径（优先使用internal目录内的配置）
if [[ -f "$INTERNAL_DIR/config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/config.env"
elif [[ -f "$INTERNAL_DIR/../config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/../config.env"
else
    CONFIG_FILE="$INTERNAL_DIR/config.env"
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[调试]${NC} $1"
}

# 加载配置
load_config() {
    # 自动创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件不存在，正在创建默认配置文件..."

        if [[ -f "$INTERNAL_DIR/config-example.env" ]]; then
            cp "$INTERNAL_DIR/config-example.env" "$CONFIG_FILE"
            chmod 600 "$CONFIG_FILE"
            log_info "已创建配置文件: $CONFIG_FILE"
            log_warn "请先配置必需参数后再运行证书管理："
            log_info "  nano $CONFIG_FILE"
            log_info "必需配置: DOMAIN_NAME, CLOUDFLARE_API_TOKEN, CERT_EMAIL"
            exit 1
        else
            log_error "配置示例文件不存在: $INTERNAL_DIR/config-example.env"
            exit 1
        fi
    fi
    
    # 安全地加载配置文件
    while IFS='=' read -r key value; do
        if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
            value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
            export "$key"="$value"
        fi
    done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')
    
    # 验证必需的配置
    if [[ -z "${DOMAIN_NAME:-}" ]] || [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        log_error "缺少必需的配置参数"
        log_info "请检查 config.env 中的 DOMAIN_NAME 和 CLOUDFLARE_API_TOKEN"
        exit 1
    fi
    
    # 设置默认值
    MATRIX_DOMAIN="${MATRIX_DOMAIN:-matrix.$DOMAIN_NAME}"
    TURN_DOMAIN="${TURN_DOMAIN:-turn.$DOMAIN_NAME}"
    CERT_EMAIL="${CERT_EMAIL:-admin@$DOMAIN_NAME}"
    CERT_THRESHOLD_DAYS="${CERT_THRESHOLD_DAYS:-30}"
    HTTPS_PORT="${HTTPS_PORT:-443}"
}

# 检测acme.sh安装路径
detect_acme_path() {
    local acme_paths=(
        "$HOME/.acme.sh"
        "/root/.acme.sh"
        "/opt/acme.sh"
        "/usr/local/acme.sh"
    )
    
    for path in "${acme_paths[@]}"; do
        if [[ -f "$path/acme.sh" ]]; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# 安装acme.sh
install_acme() {
    log_info "正在安装 acme.sh..."
    
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    curl https://get.acme.sh | sh -s email="$CERT_EMAIL"
    
    # 重新检测路径
    ACME_HOME=$(detect_acme_path)
    if [[ -z "$ACME_HOME" ]]; then
        log_error "acme.sh 安装失败"
        exit 1
    fi
    
    log_info "acme.sh 安装成功: $ACME_HOME"
}

# 检查证书有效期
check_cert_validity() {
    local domain="$1"
    local cert_path="$2"
    
    if [[ ! -f "$cert_path" ]]; then
        echo "0"
        return
    fi
    
    local expiry_date
    expiry_date=$(openssl x509 -in "$cert_path" -noout -enddate 2>/dev/null | cut -d= -f2)
    
    if [[ -z "$expiry_date" ]]; then
        echo "0"
        return
    fi
    
    local expiry_epoch
    if command -v gdate >/dev/null 2>&1; then
        # macOS with GNU date
        expiry_epoch=$(gdate -d "$expiry_date" +%s)
    else
        # Linux date
        expiry_epoch=$(date -d "$expiry_date" +%s)
    fi
    
    local current_epoch
    current_epoch=$(date +%s)
    
    local days_remaining
    days_remaining=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    echo "$days_remaining"
}

# 创建证书软链接
create_symlinks() {
    local domain="$1"
    local ssl_dir="$INTERNAL_DIR/ssl/$domain"
    
    # 创建SSL目录
    mkdir -p "$ssl_dir"
    
    # 检测acme.sh路径
    if [[ -z "${ACME_HOME:-}" ]]; then
        ACME_HOME=$(detect_acme_path)
        if [[ -z "$ACME_HOME" ]]; then
            log_error "未找到 acme.sh 安装路径"
            return 1
        fi
    fi
    
    # 智能检测证书目录（支持ECC和RSA证书）
    local cert_dir=""
    local possible_dirs=(
        "$ACME_HOME/${domain}_ecc"    # ECC证书目录
        "$ACME_HOME/$domain"          # RSA证书目录
    )

    for dir in "${possible_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            cert_dir="$dir"
            log_info "找到证书目录: $cert_dir"
            break
        fi
    done

    if [[ -z "$cert_dir" ]]; then
        log_warn "证书目录不存在，检查的路径："
        for dir in "${possible_dirs[@]}"; do
            log_warn "  - $dir"
        done
        log_warn "跳过域名 $domain 的软链接创建"
        return 0  # 改为返回成功，避免阻止其他域名处理
    fi
    
    # 创建软链接（智能检测文件名）
    local file_mappings=(
        "fullchain.cer:fullchain.pem"
        "${domain}.key:privkey.pem"
        "${domain}.cer:cert.pem"
        "ca.cer:chain.pem"
    )

    for mapping in "${file_mappings[@]}"; do
        local src_name="${mapping%:*}"
        local dst_name="${mapping#*:}"
        local src_file="$cert_dir/$src_name"
        local dst_file="$ssl_dir/$dst_name"

        if [[ -f "$src_file" ]]; then
            # 删除现有文件或链接
            [[ -e "$dst_file" ]] && rm -f "$dst_file"

            # 创建软链接
            ln -sf "$src_file" "$dst_file"
            log_info "已创建软链接: $dst_name -> $src_file"
        else
            log_warn "源文件不存在: $src_file"

            # 尝试备用文件名
            case "$src_name" in
                "${domain}.key")
                    local alt_src="$cert_dir/privkey.key"
                    if [[ -f "$alt_src" ]]; then
                        [[ -e "$dst_file" ]] && rm -f "$dst_file"
                        ln -sf "$alt_src" "$dst_file"
                        log_info "已创建软链接: $dst_name -> $alt_src (备用文件名)"
                    fi
                    ;;
                "${domain}.cer")
                    local alt_src="$cert_dir/cert.cer"
                    if [[ -f "$alt_src" ]]; then
                        [[ -e "$dst_file" ]] && rm -f "$dst_file"
                        ln -sf "$alt_src" "$dst_file"
                        log_info "已创建软链接: $dst_name -> $alt_src (备用文件名)"
                    fi
                    ;;
            esac
        fi
    done
    
    return 0
}

# 检查并确保软链接存在
ensure_symlinks() {
    local domain="$1"
    local ssl_dir="$INTERNAL_DIR/ssl/$domain"

    # 检查软链接目录是否存在
    if [[ ! -d "$ssl_dir" ]]; then
        log_info "软链接目录不存在，正在创建: $ssl_dir"
        mkdir -p "$ssl_dir"
    fi

    # 检查关键软链接是否存在且有效
    local key_files=("fullchain.pem" "privkey.pem")
    local missing_links=false

    for file in "${key_files[@]}"; do
        local link_path="$ssl_dir/$file"
        if [[ ! -L "$link_path" ]] || [[ ! -f "$link_path" ]]; then
            log_warn "软链接缺失或无效: $file"
            missing_links=true
        fi
    done

    # 如果有缺失的软链接，尝试创建
    if [[ "$missing_links" == "true" ]]; then
        log_info "检测到缺失的软链接，正在重新创建..."
        create_symlinks "$domain"
    else
        log_info "域名 $domain 的软链接状态正常"
    fi
}

# 申请证书
issue_certificate() {
    local domain="$1"
    local force="${2:-false}"
    
    # 检测acme.sh路径
    if [[ -z "${ACME_HOME:-}" ]]; then
        ACME_HOME=$(detect_acme_path)
        if [[ -z "$ACME_HOME" ]]; then
            log_warn "未找到 acme.sh，正在安装..."
            install_acme
        fi
    fi
    
    local acme_sh="$ACME_HOME/acme.sh"
    
    # 检查现有证书（智能检测ECC和RSA证书）
    local cert_path=""
    local possible_cert_paths=(
        "$ACME_HOME/${domain}_ecc/fullchain.cer"    # ECC证书
        "$ACME_HOME/$domain/fullchain.cer"          # RSA证书
    )

    for path in "${possible_cert_paths[@]}"; do
        if [[ -f "$path" ]]; then
            cert_path="$path"
            log_info "找到现有证书: $cert_path"
            break
        fi
    done

    local days_remaining
    if [[ -n "$cert_path" ]]; then
        days_remaining=$(check_cert_validity "$domain" "$cert_path")
    else
        days_remaining=0  # 没有找到证书，设置为0天
        log_info "未找到现有证书，将申请新证书"
    fi
    
    if [[ "$force" != "true" ]] && [[ "$days_remaining" -gt "$CERT_THRESHOLD_DAYS" ]]; then
        log_info "域名 $domain 的证书还有 $days_remaining 天有效期，跳过申请"
        create_symlinks "$domain"
        return 0
    fi
    
    if [[ "$days_remaining" -gt 0 ]] && [[ "$force" != "true" ]]; then
        log_warn "域名 $domain 的证书还有 $days_remaining 天有效期"
        echo -e "${YELLOW}是否强制重新申请？这将消耗 Let's Encrypt 配额。${NC}"
        echo -e "${RED}[0]${NC} 取消申请"
        echo -e "${GREEN}[1]${NC} 强制申请"
        
        read -p "请选择 [0-1]: " choice
        case $choice in
            1)
                log_info "用户确认强制申请证书"
                ;;
            *)
                log_info "用户取消申请，但将创建现有证书的软链接"
                create_symlinks "$domain"
                return 0
                ;;
        esac
    fi
    
    log_info "正在为域名 $domain 申请 SSL 证书..."
    
    # 设置Cloudflare API
    export CF_Token="$CLOUDFLARE_API_TOKEN"
    
    # 申请证书
    if "$acme_sh" --issue --dns dns_cf -d "$domain" --force; then
        log_info "证书申请成功: $domain"
        create_symlinks "$domain"
        return 0
    else
        log_error "证书申请失败: $domain"
        return 1
    fi
}

# 显示证书状态
show_cert_status() {
    local domains=("$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN")
    
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        证书状态报告${NC}"
    echo -e "${CYAN}================================${NC}"
    
    for domain in "${domains[@]}"; do
        echo -e "\n${BLUE}域名: $domain${NC}"
        
        # 检测acme.sh路径
        if [[ -z "${ACME_HOME:-}" ]]; then
            ACME_HOME=$(detect_acme_path)
        fi
        
        if [[ -n "$ACME_HOME" ]]; then
            local cert_path="$ACME_HOME/$domain/fullchain.cer"
            local days_remaining
            days_remaining=$(check_cert_validity "$domain" "$cert_path")
            
            if [[ "$days_remaining" -gt 0 ]]; then
                if [[ "$days_remaining" -gt "$CERT_THRESHOLD_DAYS" ]]; then
                    echo -e "  状态: ${GREEN}有效${NC}"
                    echo -e "  剩余天数: ${GREEN}$days_remaining 天${NC}"
                else
                    echo -e "  状态: ${YELLOW}即将过期${NC}"
                    echo -e "  剩余天数: ${YELLOW}$days_remaining 天${NC}"
                fi
                echo -e "  证书路径: $cert_path"
            else
                echo -e "  状态: ${RED}无效或不存在${NC}"
            fi
        else
            echo -e "  状态: ${RED}acme.sh 未安装${NC}"
        fi
        
        # 检查软链接
        local ssl_dir="$INTERNAL_DIR/ssl/$domain"
        if [[ -d "$ssl_dir" ]]; then
            echo -e "  软链接目录: $ssl_dir"
            local link_files=("fullchain.pem" "privkey.pem")
            for file in "${link_files[@]}"; do
                local link_path="$ssl_dir/$file"
                if [[ -L "$link_path" ]]; then
                    local target
                    target=$(readlink "$link_path")
                    if [[ -f "$target" ]]; then
                        echo -e "    ${GREEN}✓${NC} $file -> $target"
                    else
                        echo -e "    ${RED}✗${NC} $file -> $target (目标不存在)"
                    fi
                else
                    echo -e "    ${RED}✗${NC} $file (软链接不存在)"
                fi
            done
        else
            echo -e "  软链接目录: ${RED}不存在${NC}"
        fi
    done
    
    echo -e "\n${CYAN}================================${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    智能证书管理系统${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}使用方法:${NC}"
    echo -e "  $0 [命令] [选项]"
    echo
    echo -e "${GREEN}可用命令:${NC}"
    echo -e "  ${GREEN}smart${NC}           智能证书管理（推荐）"
    echo -e "  ${GREEN}status${NC}          显示证书状态"
    echo -e "  ${GREEN}issue${NC} <域名>    申请指定域名的证书"
    echo -e "  ${GREEN}force${NC} <域名>    强制重新申请证书"
    echo -e "  ${GREEN}symlinks${NC}        创建所有证书的软链接"
    echo -e "  ${GREEN}check-links${NC}     检查并修复软链接"
    echo -e "  ${GREEN}install${NC}         安装 acme.sh"
    echo -e "  ${GREEN}help${NC}            显示此帮助信息"
    echo
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  • 证书有效期超过 $CERT_THRESHOLD_DAYS 天时不会重新申请"
    echo -e "  • 使用软链接机制确保证书实时同步"
    echo -e "  • 强制申请会消耗 Let's Encrypt 配额，请谨慎使用"
    echo
}

# 智能证书管理
smart_management() {
    log_info "开始智能证书管理..."
    
    local domains=("$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN")
    local success_count=0
    
    for domain in "${domains[@]}"; do
        log_info "处理域名: $domain"
        if issue_certificate "$domain"; then
            ((success_count++))
        else
            log_warn "域名 $domain 处理未完全成功，但将继续处理其他域名"
        fi

        # 确保软链接存在（即使证书申请失败也要检查现有证书的软链接）
        if ensure_symlinks "$domain"; then
            log_info "域名 $domain 的软链接检查完成"
        else
            log_warn "域名 $domain 的软链接检查有问题，但不影响整体部署"
        fi
    done

    log_info "智能证书管理完成，处理了 ${#domains[@]} 个域名，其中 $success_count 个完全成功"

    # 只要有一个域名成功处理，就认为整体成功
    if [[ "$success_count" -gt 0 ]]; then
        log_info "证书管理整体成功"
        return 0
    else
        log_error "所有域名都处理失败"
        return 1
    fi
}

# 主函数
main() {
    # 加载配置
    load_config
    
    case "${1:-help}" in
        "smart")
            smart_management
            ;;
        "status")
            show_cert_status
            ;;
        "issue")
            if [[ -z "${2:-}" ]]; then
                log_error "请指定域名"
                exit 1
            fi
            issue_certificate "$2"
            ;;
        "force")
            if [[ -z "${2:-}" ]]; then
                log_error "请指定域名"
                exit 1
            fi
            issue_certificate "$2" "true"
            ;;
        "symlinks")
            local domains=("$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN")
            for domain in "${domains[@]}"; do
                create_symlinks "$domain"
            done
            ;;
        "check-links")
            local domains=("$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN")
            for domain in "${domains[@]}"; do
                log_info "检查域名 $domain 的软链接..."
                ensure_symlinks "$domain"
            done
            ;;
        "install")
            install_acme
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
