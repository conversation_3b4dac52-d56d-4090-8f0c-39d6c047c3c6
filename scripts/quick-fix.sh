#!/bin/bash

# Synapse自动部署系统 - 快速问题修复脚本
# 解决环境变量、Docker配置和证书管理问题

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    快速问题修复工具${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
}

# 检查并修复配置文件
fix_config() {
    log_step "检查配置文件"
    
    if [[ ! -f "$SCRIPT_DIR/config.env" ]]; then
        log_warn "配置文件不存在，正在创建..."
        if [[ -f "$SCRIPT_DIR/config-example.env" ]]; then
            cp "$SCRIPT_DIR/config-example.env" "$SCRIPT_DIR/config.env"
            chmod 600 "$SCRIPT_DIR/config.env"
            log_info "已创建配置文件: config.env"
        else
            log_error "配置示例文件不存在"
            return 1
        fi
    fi
    
    # 安全地加载配置文件
    while IFS='=' read -r key value; do
        if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
            value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
            export "$key"="$value"
        fi
    done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$SCRIPT_DIR/config.env" | grep -v '^#')
    
    local config_issues=()
    
    if [[ "${DOMAIN_NAME:-}" == "example.com" ]] || [[ -z "${DOMAIN_NAME:-}" ]]; then
        config_issues+=("DOMAIN_NAME需要设置为您的实际域名")
    fi
    
    if [[ "${CLOUDFLARE_API_TOKEN:-}" == "your_cloudflare_api_token_here" ]] || [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        config_issues+=("CLOUDFLARE_API_TOKEN需要设置为您的Cloudflare API密钥")
    fi
    
    if [[ ${#config_issues[@]} -gt 0 ]]; then
        log_warn "发现配置问题："
        for issue in "${config_issues[@]}"; do
            echo -e "  • $issue"
        done
        echo
        log_info "请编辑配置文件: nano $SCRIPT_DIR/config.env"
        return 1
    else
        log_info "配置文件检查通过"
        return 0
    fi
}

# 修复Docker Compose配置
fix_docker_compose() {
    log_step "修复Docker Compose配置"
    
    local compose_file="$SCRIPT_DIR/docker-compose.yml"
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Docker Compose文件不存在: $compose_file"
        return 1
    fi
    
    # 检查是否有过时的version字段
    if grep -q "^version:" "$compose_file"; then
        log_warn "发现过时的version字段，正在移除..."
        sed -i.bak '/^version:/d' "$compose_file"
        log_info "已移除过时的version字段"
    fi
    
    # 验证配置语法
    if docker compose -f "$compose_file" config > /dev/null 2>&1; then
        log_info "Docker Compose配置语法正确"
        return 0
    else
        log_error "Docker Compose配置语法错误"
        return 1
    fi
}

# 检查Docker服务状态
check_docker_services() {
    log_step "检查Docker服务状态"
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行，请启动Docker"
        return 1
    fi
    
    log_info "Docker服务正常运行"
    
    # 检查容器状态
    local containers=$(docker compose ps --services 2>/dev/null || echo "")
    
    if [[ -z "$containers" ]]; then
        log_warn "没有运行的容器"
        log_info "建议运行: docker compose up -d"
    else
        log_info "发现以下服务:"
        docker compose ps --format "table {{.Service}}\t{{.Status}}\t{{.Ports}}"
    fi
    
    return 0
}

# 修复证书软链接
fix_certificates() {
    log_step "检查证书软链接"
    
    if [[ ! -f "$SCRIPT_DIR/scripts/certificate-manager.sh" ]]; then
        log_error "证书管理脚本不存在"
        return 1
    fi
    
    # 检查软链接状态
    log_info "检查证书软链接状态..."
    if "$SCRIPT_DIR/scripts/certificate-manager.sh" check-links; then
        log_info "证书软链接检查完成"
    else
        log_warn "证书软链接可能需要修复"
    fi
    
    return 0
}

# 生成修复报告
generate_report() {
    log_step "生成修复报告"
    
    echo -e "${CYAN}修复完成情况：${NC}"
    
    # 检查配置文件
    if [[ -f "$SCRIPT_DIR/config.env" ]]; then
        echo -e "  ${GREEN}✓${NC} 配置文件存在"
    else
        echo -e "  ${RED}✗${NC} 配置文件缺失"
    fi
    
    # 检查Docker配置
    if docker compose config > /dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} Docker Compose配置正确"
    else
        echo -e "  ${RED}✗${NC} Docker Compose配置有问题"
    fi
    
    # 检查Docker服务
    if docker info > /dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} Docker服务运行正常"
    else
        echo -e "  ${RED}✗${NC} Docker服务未运行"
    fi
    
    echo
    log_info "修复建议："
    echo -e "  1. 确保配置文件中的域名和API密钥正确"
    echo -e "  2. 运行: ${WHITE}docker compose up -d${NC} 启动服务"
    echo -e "  3. 运行: ${WHITE}./scripts/certificate-manager.sh smart${NC} 管理证书"
    echo
}

# 主函数
main() {
    show_banner
    
    log_info "开始快速问题修复..."
    echo
    
    # 执行修复步骤
    fix_config
    fix_docker_compose
    check_docker_services
    fix_certificates
    
    # 生成报告
    generate_report
    
    log_info "快速修复完成！"
}

# 执行主函数
main "$@"
