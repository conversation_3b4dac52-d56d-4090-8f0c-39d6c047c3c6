# Synapse 自动部署系统

一个企业级的Matrix Synapse服务器自动化部署解决方案，支持完整的SSL证书管理、Docker容器化部署、RouterOS网络集成和智能监控系统。

## 🌟 主要特性

### 🚀 一键部署
- **智能化部署**：单个命令完成从零到可用的完整部署
- **环境自适应**：自动检测系统环境并安装必要依赖
- **中断恢复**：支持部署过程的中断恢复机制

### 🔐 智能证书管理
- **配额保护**：智能检测现有证书，避免Let's Encrypt配额浪费
- **软链接机制**：实现证书文件的实时同步，避免重复申请
- **多域名支持**：独立管理基础域名、Matrix域名、TURN域名证书
- **自动续期**：定时检查和自动续期即将过期的证书

### 🐳 Docker容器化
- **V2标准**：严格遵循Docker Compose V2标准，无警告配置
- **官方镜像**：使用官方推荐的Docker镜像，支持镜像源切换
- **健康检查**：容器健康检查和自动重启机制

### 🌐 网络集成
- **RouterOS支持**：集成RouterOS 7.x API，实现动态IP监控
- **ISP适配**：处理ISP阻塞80/443端口的情况
- **Well-known配置**：符合Matrix规范的服务发现配置

### 🎯 用户友好
- **中文界面**：完整的中文用户界面和提示信息
- **菜单导航**：直观的菜单式交互，替代复杂的命令行参数
- **小白友好**：为非技术用户设计的简化操作流程

## 📋 系统要求

### 操作系统
- Ubuntu 20.04+ 
- Debian 11+ (Debian 12特别支持)

### 硬件要求
- **内存**：最低2GB，推荐4GB+
- **存储**：最低20GB可用空间
- **网络**：稳定的互联网连接

### 软件依赖
- Docker Engine 24.0+
- Docker Compose V2
- Python 3.9+
- curl, openssl, git

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd snapse

# 复制配置文件
cp config-example.env config.env

# 编辑配置文件
nano config.env
```

### 2. 配置域名和API

在 `config.env` 中配置：
- 基础域名和子域名
- Cloudflare API Token
- 管理员账户信息

### 3. 一键部署

```bash
# 进入部署目录
cd internal

# 执行快速安装
./install.sh

# 或使用完整部署
./deploy.sh
```

### 4. 访问服务

部署完成后，您可以通过以下地址访问：
- **Matrix服务**：https://matrix.yourdomain.com
- **Element客户端**：https://app.element.io （配置服务器地址）

## 📚 详细文档

- [手动部署指南](manual-guide.md) - 详细的手动部署步骤
- [证书管理指南](docs/certificate-guide.md) - 证书管理最佳实践
- [RouterOS集成指南](docs/routeros-guide.md) - RouterOS网络集成配置
- [故障排除指南](docs/troubleshooting.md) - 常见问题解决方案

## 🛠️ 高级功能

### 证书管理
```bash
# 独立证书管理
cd internal/scripts
./certificate-manager.sh

# 智能证书管理（推荐）
./certificate-manager.sh smart

# 检查证书状态
./certificate-manager.sh status

# 创建证书软链接
./certificate-manager.sh symlinks

# 强制重新申请证书（谨慎使用）
./certificate-manager.sh force example.com
```

### 系统维护
```bash
# 系统备份
./scripts/backup.sh backup

# 列出备份
./scripts/backup.sh list

# 健康检查
./scripts/health-check.sh

# 用户管理
./scripts/user-manager.sh
```

### 用户管理
```bash
# 启动用户管理界面
cd internal/scripts
./user-manager.sh

# 功能包括：
# - 创建新用户
# - 重置用户密码
# - 设置管理员权限
# - 列出所有用户
# - 停用/激活用户
# - 删除用户
# - 用户统计信息
```

### RouterOS集成（可选）
```bash
# 启动IP监控
cd internal/routeros
python3 ip-monitor.py

# 配置要求：
# - 在config.env中设置ENABLE_ROUTEROS=true
# - 配置RouterOS API访问信息
# - 支持RouterOS 7.x REST API
```

## 🔧 配置说明

### 核心配置文件
- `config.env` - 主要环境变量配置
- `internal/docker-compose.yml` - Docker服务编排
- `internal/configs/` - 服务配置模板

### 重要目录
- `internal/` - 核心部署文件
- `internal/configs/` - 配置模板
- `internal/scripts/` - 管理脚本
- `internal/routeros/` - RouterOS集成

## 🆘 技术支持

### 常见问题解决

#### 1. 证书相关问题
**问题**: 证书申请失败
**解决方案**:
```bash
# 检查Cloudflare API Token
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
     -H "Authorization: Bearer YOUR_API_TOKEN"

# 检查DNS配置
nslookup your-domain.com

# 查看证书状态
cd internal/scripts
./certificate-manager.sh status
```

#### 2. Docker相关问题
**问题**: Docker启动失败
**解决方案**:
```bash
# 检查Docker版本
docker --version
docker compose version

# 检查容器状态
cd internal
docker compose ps
docker compose logs

# 重启服务
docker compose restart
```

#### 3. Debian 12 Python环境问题
**问题**: Python脚本执行失败
**解决方案**:
```bash
# 创建虚拟环境
python3 -m venv /opt/matrix/venv
source /opt/matrix/venv/bin/activate

# 安装依赖
pip install -r internal/routeros/requirements.txt

# 或使用系统包
sudo apt install python3-requests python3-routeros-api
```

#### 4. 网络连接问题
**问题**: 服务无法访问
**解决方案**:
```bash
# 检查端口占用
sudo netstat -tlnp | grep -E ':(80|443|8008|3478)'

# 检查防火墙
sudo ufw status

# 测试内部连接
curl -f http://localhost:8008/health
```

### 获取帮助
- **健康检查**: `./scripts/health-check.sh`
- **查看日志**: `docker compose logs -f`
- **系统状态**: `docker compose ps`
- **备份数据**: `./scripts/backup.sh backup`

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**注意**：首次部署前请仔细阅读配置说明，确保所有必需的API密钥和域名配置正确。
