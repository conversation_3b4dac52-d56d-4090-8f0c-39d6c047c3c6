# 自定义配置项总结

## 🎯 自定义配置概述

根据您的要求，我已经为Synapse自动部署系统配置了以下自定义默认值，确保系统开箱即用且符合您的使用偏好。

## 📋 自定义配置项详情

### 1. **域名配置**
```bash
# 基础域名（已设置为您的实际域名）
DOMAIN_NAME=niub.one
MATRIX_DOMAIN=matrix.niub.one
TURN_DOMAIN=turn.niub.one
```

### 2. **TURN服务配置**
```bash
# TURN服务端口（标准端口）
TURN_PORT=3478

# TURN UDP端口范围（自定义范围）
TURN_UDP_MIN_PORT=65335
TURN_UDP_MAX_PORT=65535
```

**说明**: 使用65335-65535端口范围，避免与系统常用端口冲突，提供200个端口用于TURN中继。

### 3. **Synapse功能配置**
```bash
# 联邦功能（默认开启）
ENABLE_FEDERATION=true

# 用户注册（默认开启）
ENABLE_REGISTRATION=true

# 注册类型（仅邀请注册）
REGISTRATION_TYPE=invite_only

# 访客访问（默认关闭）
ALLOW_GUEST_ACCESS=false

# 统计报告（默认关闭，保护隐私）
ENABLE_STATS_REPORTING=false

# 最大上传文件大小
MAX_UPLOAD_SIZE=50M
```

### 4. **安全和隐私配置**
```bash
# 管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=Matrix2025!

# 证书邮箱
CERT_EMAIL=<EMAIL>

# 证书有效期阈值（30天）
CERT_THRESHOLD_DAYS=30
```

### 5. **网络和端口配置**
```bash
# HTTPS端口（标准端口）
HTTPS_PORT=443
HTTP_PORT=80

# Synapse内部端口
SYNAPSE_PORT=8008
```

## 🔧 配置文件模板更新

### homeserver.yaml.template 更新
- ✅ 支持动态联邦启用/禁用
- ✅ 支持注册类型配置（invite_only）
- ✅ 支持统计报告开关
- ✅ 优化注册安全设置

### coturn.conf.template 更新
- ✅ 使用自定义UDP端口范围（65335-65535）
- ✅ 支持动态端口配置
- ✅ 优化中继性能设置

### deploy.sh 更新
- ✅ 支持所有新配置变量的处理
- ✅ 提供合理的默认值
- ✅ 自动生成配置文件时应用自定义设置

## 🎯 功能特性

### 联邦功能（默认开启）
- **优势**: 可以与其他Matrix服务器通信
- **用途**: 跨服务器聊天、房间联邦
- **控制**: 通过ENABLE_FEDERATION控制

### 注册功能（仅邀请）
- **类型**: invite_only（仅邀请注册）
- **安全**: 防止恶意注册
- **管理**: 管理员可以创建邀请码

### TURN服务优化
- **端口范围**: 65335-65535（200个端口）
- **性能**: 足够的端口数量支持大量并发连接
- **兼容性**: 避免与系统服务冲突

## 📊 配置验证状态

### 当前配置检查结果
```
✅ 基础域名: niub.one
✅ Matrix域名: matrix.niub.one  
✅ TURN域名: turn.niub.one
✅ 证书邮箱: <EMAIL>
✅ 管理员账户: admin
✅ 管理员密码: 已设置
❌ Cloudflare API Token: 需要配置
```

### 需要手动配置的项目
只需要配置一个项目：
```bash
# 编辑配置文件
nano config.env

# 设置Cloudflare API Token
CLOUDFLARE_API_TOKEN=您的实际API密钥
```

## 🚀 部署流程

### 1. 配置API密钥
```bash
# 编辑配置文件
nano config.env

# 找到并修改这一行
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here
# 改为您的实际API密钥
CLOUDFLARE_API_TOKEN=您的实际密钥
```

### 2. 验证配置
```bash
# 运行配置检查
./config-check.sh
```

### 3. 开始部署
```bash
# 智能部署（推荐）
./install.sh

# 或手动部署
./deploy.sh
```

## 🔍 自定义配置的优势

### 1. **开箱即用**
- 所有重要配置都有合理的默认值
- 减少用户配置工作量
- 降低配置错误风险

### 2. **安全优先**
- 默认启用邀请制注册
- 关闭不必要的功能
- 使用安全的端口范围

### 3. **性能优化**
- TURN端口范围优化
- 合理的文件上传限制
- 优化的数据库配置

### 4. **易于维护**
- 清晰的配置结构
- 详细的配置说明
- 智能的配置验证

## 📝 配置文件位置

```
internal/
├── config.env                    # 主配置文件
├── config-example.env            # 配置示例
├── configs/
│   ├── homeserver.yaml.template  # Synapse配置模板
│   ├── coturn.conf.template      # TURN配置模板
│   ├── nginx.conf.template       # Nginx配置模板
│   └── log.config.template       # 日志配置模板
└── deploy.sh                     # 部署脚本（处理配置替换）
```

## 🎉 总结

通过这些自定义配置，您的Synapse服务器将具备：

- ✅ **安全的注册机制**（仅邀请）
- ✅ **完整的联邦功能**（跨服务器通信）
- ✅ **优化的TURN服务**（65335-65535端口）
- ✅ **隐私保护**（关闭统计报告）
- ✅ **易于管理**（智能配置检查）

**只需配置Cloudflare API Token，即可开始部署您的定制化Matrix服务器！** 🚀
