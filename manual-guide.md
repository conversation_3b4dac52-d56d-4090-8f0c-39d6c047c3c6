# Synapse 手动部署指南

本指南提供了Matrix Synapse服务器的详细手动部署步骤，适用于需要自定义配置或了解部署细节的用户。

## 📋 前置要求

### 系统要求
- **操作系统**: Ubuntu 20.04+ 或 Debian 11+
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低20GB可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- Docker Engine 24.0+
- Docker Compose V2
- Python 3.9+
- curl, openssl, git
- sqlite3 (默认数据库)

### 域名和DNS配置
- 准备3个域名：
  - 基础域名: `example.com`
  - Matrix域名: `matrix.example.com`
  - TURN域名: `turn.example.com`
- 将域名解析到Cloudflare
- 获取Cloudflare API Token

## 🔧 步骤1: 环境准备

### 1.1 安装Docker
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装依赖
sudo apt install -y ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 添加Docker仓库
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

### 1.2 验证安装
```bash
# 验证Docker版本
docker --version
docker compose version

# 测试Docker
docker run hello-world
```

### 1.3 安装其他依赖
```bash
# 安装基础工具
sudo apt install -y curl openssl git sqlite3 python3 python3-pip python3-venv

# 验证Python版本
python3 --version
```

## 📁 步骤2: 项目配置

### 2.1 下载项目
```bash
# 克隆项目（或下载压缩包）
git clone <repository-url> synapse-deploy
cd synapse-deploy
```

### 2.2 配置环境变量
```bash
# 复制配置模板
cp config-example.env config.env

# 编辑配置文件
nano config.env
```

### 2.3 关键配置项说明
```bash
# 基础域名配置
DOMAIN_NAME=example.com
MATRIX_DOMAIN=matrix.example.com
TURN_DOMAIN=turn.example.com

# SSL证书配置
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CERT_EMAIL=<EMAIL>

# 端口配置（如果ISP阻塞标准端口）
HTTPS_PORT=443
HTTP_PORT=80

# 管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# RouterOS集成（可选）
ENABLE_ROUTEROS=false
ROUTEROS_HOST=***********
ROUTEROS_USERNAME=api_user
ROUTEROS_PASSWORD=your_routeros_password
```

## 🔐 步骤3: SSL证书管理

### 3.1 安装acme.sh
```bash
# 安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载shell
source ~/.bashrc
```

### 3.2 申请证书
```bash
# 进入脚本目录
cd internal/scripts

# 设置Cloudflare API
export CF_Token="your_cloudflare_api_token"

# 智能证书管理（推荐）
./certificate-manager.sh smart

# 或手动申请单个域名证书
./certificate-manager.sh issue example.com
./certificate-manager.sh issue matrix.example.com
./certificate-manager.sh issue turn.example.com
```

### 3.3 验证证书
```bash
# 检查证书状态
./certificate-manager.sh status

# 验证软链接
ls -la ../ssl/*/
```

## 🐳 步骤4: Docker服务配置

### 4.1 生成配置文件
```bash
# 返回项目根目录
cd ../..

# 进入部署目录
cd internal

# 生成随机密钥
REGISTRATION_SHARED_SECRET=$(openssl rand -hex 32)
TURN_SHARED_SECRET=$(openssl rand -hex 32)

# 添加到配置文件
echo "REGISTRATION_SHARED_SECRET=$REGISTRATION_SHARED_SECRET" >> ../config.env
echo "TURN_SHARED_SECRET=$TURN_SHARED_SECRET" >> ../config.env
```

### 4.2 处理配置模板
```bash
# 加载配置
source ../config.env

# 生成Synapse配置
sed -e "s/SYNAPSE_SERVER_NAME/$MATRIX_DOMAIN/g" \
    -e "s/SYNAPSE_REPORT_STATS/${SYNAPSE_REPORT_STATS:-no}/g" \
    -e "s/TURN_DOMAIN/$TURN_DOMAIN/g" \
    -e "s/REGISTRATION_SHARED_SECRET/$REGISTRATION_SHARED_SECRET/g" \
    -e "s/TURN_SHARED_SECRET/$TURN_SHARED_SECRET/g" \
    -e "s/CERT_EMAIL/$CERT_EMAIL/g" \
    configs/homeserver.yaml.template > homeserver.yaml

# 生成Nginx配置
sed -e "s/DOMAIN_NAME/$DOMAIN_NAME/g" \
    -e "s/MATRIX_DOMAIN/$MATRIX_DOMAIN/g" \
    -e "s/HTTPS_PORT/${HTTPS_PORT:-443}/g" \
    configs/nginx.conf.template > nginx.conf

# 生成Coturn配置
EXTERNAL_IP=$(curl -s ifconfig.me)
sed -e "s/TURN_DOMAIN/$TURN_DOMAIN/g" \
    -e "s/TURN_SHARED_SECRET/$TURN_SHARED_SECRET/g" \
    -e "s/EXTERNAL_IP/$EXTERNAL_IP/g" \
    configs/coturn.conf.template > coturn.conf

# 复制日志配置
cp configs/log.config.template log.config
```

### 4.3 创建必要目录
```bash
# 创建数据目录
mkdir -p data ssl logs media_store uploads

# 设置权限
chmod 755 data ssl logs media_store uploads
```

## 🚀 步骤5: 启动服务

### 5.1 启动Docker容器
```bash
# 停止现有服务（如果有）
docker compose down

# 启动服务
docker compose up -d

# 检查服务状态
docker compose ps
```

### 5.2 等待服务启动
```bash
# 查看日志
docker compose logs -f

# 等待Synapse完全启动（通常需要1-2分钟）
sleep 120
```

### 5.3 验证服务
```bash
# 检查Synapse健康状态
curl -f http://localhost:8008/health

# 检查HTTPS服务
curl -f -k https://localhost:443

# 检查Well-known端点
curl -f https://$DOMAIN_NAME/.well-known/matrix/server
```

## 👤 步骤6: 用户管理

### 6.1 创建管理员用户
```bash
# 使用用户管理脚本
./scripts/user-manager.sh

# 或手动创建
docker compose exec synapse register_new_matrix_user \
    -u admin \
    -p your_password \
    -a \
    -c /data/homeserver.yaml \
    http://localhost:8008
```

### 6.2 测试登录
- 打开 https://app.element.io
- 选择"自定义服务器"
- 输入服务器地址: `https://matrix.example.com`
- 使用创建的管理员账户登录

## 🔧 步骤7: 系统维护

### 7.1 设置备份
```bash
# 执行备份
./scripts/backup.sh backup

# 设置定时备份（可选）
crontab -e
# 添加：0 2 * * * /path/to/synapse-deploy/internal/scripts/backup.sh backup
```

### 7.2 健康检查
```bash
# 运行健康检查
./scripts/health-check.sh

# 设置定时检查（可选）
crontab -e
# 添加：*/30 * * * * /path/to/synapse-deploy/internal/scripts/health-check.sh
```

### 7.3 RouterOS集成（可选）
```bash
# 如果启用RouterOS集成
cd routeros

# 创建Python虚拟环境（Debian 12需要）
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动IP监控
python3 ip-monitor.py

# 设置为系统服务（可选）
sudo cp ip-monitor.service /etc/systemd/system/
sudo systemctl enable ip-monitor
sudo systemctl start ip-monitor
```

## 🔍 故障排除

### 常见问题
1. **容器启动失败**: 检查端口占用和配置文件
2. **证书问题**: 验证Cloudflare API和DNS配置
3. **网络问题**: 检查防火墙和路由配置
4. **权限问题**: 确认文件权限和用户组

### 调试命令
```bash
# 查看容器日志
docker compose logs synapse
docker compose logs nginx
docker compose logs coturn

# 检查配置文件
nginx -t
docker compose config

# 网络诊断
netstat -tlnp | grep -E ':(80|443|8008|3478)'
curl -I https://matrix.example.com
```

## 📚 参考资料

- [Matrix Synapse官方文档](https://element-hq.github.io/synapse/latest/)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [Cloudflare API文档](https://api.cloudflare.com/)
- [RouterOS API文档](https://help.mikrotik.com/docs/display/ROS/REST+API)

---

**注意**: 本指南假设您具有基本的Linux系统管理经验。如果遇到问题，请参考自动部署脚本或寻求技术支持。
