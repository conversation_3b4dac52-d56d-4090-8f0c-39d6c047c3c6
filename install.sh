#!/bin/bash

# Synapse自动部署系统 - 快速安装脚本
# 提供简化的安装流程和中文菜单界面

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示主菜单
show_main_menu() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 快速安装向导${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "${GREEN}版本: 1.0.0 | 基于: Matrix Synapse v1.110+${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}[1]${NC} 🚀 快速部署 (推荐新用户)"
    echo -e "${GREEN}[2]${NC} 🔧 自定义部署"
    echo -e "${GREEN}[3]${NC} 🔐 证书管理"
    echo -e "${GREEN}[4]${NC} 📊 系统状态"
    echo -e "${GREEN}[5]${NC} 🛠️  系统维护"
    echo -e "${GREEN}[6]${NC} 📚 帮助文档"
    echo -e "${RED}[0]${NC} 🚪 退出"
    echo
    echo -e "${CYAN}================================${NC}"
}

# 显示证书管理菜单
show_cert_menu() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        证书管理系统${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}[1]${NC} 🔍 查看证书状态"
    echo -e "${GREEN}[2]${NC} 🎯 智能证书管理 (推荐)"
    echo -e "${GREEN}[3]${NC} 📝 申请新证书"
    echo -e "${GREEN}[4]${NC} 🔗 创建证书软链接"
    echo -e "${GREEN}[5]${NC} ⚡ 强制重新申请证书"
    echo -e "${GREEN}[6]${NC} 🔧 安装 acme.sh"
    echo -e "${RED}[0]${NC} 🔙 返回主菜单"
    echo
    echo -e "${CYAN}================================${NC}"
}

# 显示系统状态
show_system_status() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        系统状态信息${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    
    # Docker状态
    echo -e "${BLUE}Docker 服务状态:${NC}"
    if command -v docker >/dev/null 2>&1; then
        if docker compose ps 2>/dev/null; then
            echo -e "${GREEN}✓ Docker 服务正常运行${NC}"
        else
            echo -e "${YELLOW}⚠ Docker 服务未启动或配置错误${NC}"
        fi
    else
        echo -e "${RED}✗ Docker 未安装${NC}"
    fi
    echo
    
    # 证书状态
    echo -e "${BLUE}证书状态:${NC}"
    if [[ -f "$SCRIPT_DIR/scripts/certificate-manager.sh" ]]; then
        "$SCRIPT_DIR/scripts/certificate-manager.sh" status 2>/dev/null || echo -e "${RED}✗ 证书状态检查失败${NC}"
    else
        echo -e "${RED}✗ 证书管理脚本不存在${NC}"
    fi
    echo
    
    # 配置文件状态
    echo -e "${BLUE}配置文件状态:${NC}"
    local config_files=("$SCRIPT_DIR/config.env" "$SCRIPT_DIR/homeserver.yaml" "$SCRIPT_DIR/nginx.conf")
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            echo -e "${GREEN}✓ $(basename "$file")${NC}"
        else
            echo -e "${RED}✗ $(basename "$file")${NC}"
        fi
    done
    echo
    
    echo -e "${CYAN}按任意键返回主菜单...${NC}"
    read -n 1
}

# 快速部署向导
quick_deploy_wizard() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        快速部署向导${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    
    # 检查配置文件
    if [[ ! -f "$SCRIPT_DIR/config.env" ]]; then
        log_warn "配置文件不存在，开始智能配置向导..."

        # 复制示例配置
        if [[ -f "$SCRIPT_DIR/config-example.env" ]]; then
            cp "$SCRIPT_DIR/config-example.env" "$SCRIPT_DIR/config.env"
            chmod 600 "$SCRIPT_DIR/config.env"
            log_info "已创建配置文件: config.env"
        else
            log_error "示例配置文件不存在"
            return 1
        fi
        
        # 配置向导
        echo -e "\n${YELLOW}请配置以下必需参数:${NC}"
        
        # 域名配置
        echo -e "\n${BLUE}1. 基础域名配置${NC}"
        read -p "请输入您的域名 (例如: example.com): " domain_name
        if [[ -z "$domain_name" ]]; then
            log_error "域名不能为空"
            return 1
        fi
        
        # Cloudflare API Token
        echo -e "\n${BLUE}2. Cloudflare API Token${NC}"
        echo -e "${YELLOW}获取地址: https://dash.cloudflare.com/profile/api-tokens${NC}"
        read -p "请输入 Cloudflare API Token: " cf_token
        if [[ -z "$cf_token" ]]; then
            log_error "Cloudflare API Token 不能为空"
            return 1
        fi
        
        # 邮箱配置
        echo -e "\n${BLUE}3. 邮箱配置${NC}"
        read -p "请输入邮箱地址 (用于证书通知): " cert_email
        if [[ -z "$cert_email" ]]; then
            cert_email="admin@$domain_name"
        fi
        
        # 管理员配置
        echo -e "\n${BLUE}4. 管理员账户配置${NC}"
        read -p "请输入管理员用户名 (默认: admin): " admin_user
        admin_user="${admin_user:-admin}"
        
        read -s -p "请输入管理员密码: " admin_pass
        echo
        if [[ -z "$admin_pass" ]]; then
            log_error "管理员密码不能为空"
            return 1
        fi
        
        # 更新配置文件（使用安全的方法）
        # 转义特殊字符
        domain_name_escaped=$(printf '%s\n' "$domain_name" | sed 's/[[\.*^$()+?{|]/\\&/g')
        cf_token_escaped=$(printf '%s\n' "$cf_token" | sed 's/[[\.*^$()+?{|]/\\&/g')
        cert_email_escaped=$(printf '%s\n' "$cert_email" | sed 's/[[\.*^$()+?{|]/\\&/g')
        admin_user_escaped=$(printf '%s\n' "$admin_user" | sed 's/[[\.*^$()+?{|]/\\&/g')
        admin_pass_escaped=$(printf '%s\n' "$admin_pass" | sed 's/[[\.*^$()+?{|]/\\&/g')

        sed -i "s|DOMAIN_NAME=.*|DOMAIN_NAME=$domain_name_escaped|" "$SCRIPT_DIR/config.env"
        sed -i "s|CLOUDFLARE_API_TOKEN=.*|CLOUDFLARE_API_TOKEN=$cf_token_escaped|" "$SCRIPT_DIR/config.env"
        sed -i "s|CERT_EMAIL=.*|CERT_EMAIL=$cert_email_escaped|" "$SCRIPT_DIR/config.env"
        sed -i "s|ADMIN_USERNAME=.*|ADMIN_USERNAME=$admin_user_escaped|" "$SCRIPT_DIR/config.env"
        sed -i "s|ADMIN_PASSWORD=.*|ADMIN_PASSWORD=$admin_pass_escaped|" "$SCRIPT_DIR/config.env"
        
        log_info "配置文件更新完成"
    fi
    
    # 确认部署
    echo -e "\n${YELLOW}准备开始部署，这将:${NC}"
    echo -e "  • 检查和安装系统依赖"
    echo -e "  • 申请SSL证书"
    echo -e "  • 启动Matrix Synapse服务"
    echo -e "  • 创建管理员账户"
    echo
    echo -e "${YELLOW}是否继续？${NC}"
    echo -e "${RED}[0]${NC} 取消"
    echo -e "${GREEN}[1]${NC} 开始部署"
    
    read -p "请选择 [0-1]: " choice
    case $choice in
        1)
            log_info "开始快速部署..."
            if "$SCRIPT_DIR/deploy.sh"; then
                log_info "快速部署完成！"
                echo -e "\n${CYAN}按任意键返回主菜单...${NC}"
                read -n 1
            else
                log_error "部署失败"
                echo -e "\n${CYAN}按任意键返回主菜单...${NC}"
                read -n 1
            fi
            ;;
        *)
            log_info "用户取消部署"
            ;;
    esac
}

# 自定义部署
custom_deploy() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        自定义部署${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    
    log_info "自定义部署模式"
    echo -e "${YELLOW}请手动编辑配置文件后运行部署脚本:${NC}"
    echo -e "  1. 编辑配置: ${WHITE}nano $SCRIPT_DIR/config.env${NC}"
    echo -e "  2. 运行部署: ${WHITE}$SCRIPT_DIR/deploy.sh${NC}"
    echo
    echo -e "${CYAN}按任意键返回主菜单...${NC}"
    read -n 1
}

# 证书管理
manage_certificates() {
    while true; do
        show_cert_menu
        read -p "请选择操作 [0-6]: " choice
        
        case $choice in
            1)
                "$SCRIPT_DIR/scripts/certificate-manager.sh" status
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            2)
                "$SCRIPT_DIR/scripts/certificate-manager.sh" smart
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            3)
                echo -e "\n请输入要申请证书的域名:"
                read -p "域名: " domain
                if [[ -n "$domain" ]]; then
                    "$SCRIPT_DIR/scripts/certificate-manager.sh" issue "$domain"
                fi
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            4)
                "$SCRIPT_DIR/scripts/certificate-manager.sh" symlinks
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            5)
                echo -e "\n${RED}警告: 强制重新申请会消耗 Let's Encrypt 配额${NC}"
                echo -e "请输入要强制申请证书的域名:"
                read -p "域名: " domain
                if [[ -n "$domain" ]]; then
                    "$SCRIPT_DIR/scripts/certificate-manager.sh" force "$domain"
                fi
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            6)
                "$SCRIPT_DIR/scripts/certificate-manager.sh" install
                echo -e "\n${CYAN}按任意键继续...${NC}"
                read -n 1
                ;;
            0)
                break
                ;;
            *)
                log_error "无效选择，请重试"
                sleep 1
                ;;
        esac
    done
}

# 系统维护
system_maintenance() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        系统维护${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    
    echo -e "${GREEN}[1]${NC} 🔄 重启服务"
    echo -e "${GREEN}[2]${NC} 🛑 停止服务"
    echo -e "${GREEN}[3]${NC} 📋 查看日志"
    echo -e "${GREEN}[4]${NC} 🧹 清理系统"
    echo -e "${RED}[0]${NC} 🔙 返回主菜单"
    echo
    
    read -p "请选择操作 [0-4]: " choice
    
    case $choice in
        1)
            log_info "正在重启服务..."
            cd "$SCRIPT_DIR"
            docker compose restart
            log_info "服务重启完成"
            ;;
        2)
            log_info "正在停止服务..."
            cd "$SCRIPT_DIR"
            docker compose down
            log_info "服务已停止"
            ;;
        3)
            log_info "显示服务日志 (按 Ctrl+C 退出)..."
            cd "$SCRIPT_DIR"
            docker compose logs -f
            ;;
        4)
            log_info "清理Docker资源..."
            docker system prune -f
            log_info "清理完成"
            ;;
        0)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
    
    echo -e "\n${CYAN}按任意键返回主菜单...${NC}"
    read -n 1
}

# 显示帮助
show_help() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}        帮助文档${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    
    echo -e "${BLUE}快速开始:${NC}"
    echo -e "  1. 选择 '快速部署' 进行自动配置"
    echo -e "  2. 按照向导输入域名和API密钥"
    echo -e "  3. 等待部署完成"
    echo
    echo -e "${BLUE}重要文件:${NC}"
    echo -e "  配置文件: ${WHITE}$SCRIPT_DIR/config.env${NC}"
    echo -e "  部署脚本: ${WHITE}$SCRIPT_DIR/deploy.sh${NC}"
    echo -e "  证书管理: ${WHITE}$SCRIPT_DIR/scripts/certificate-manager.sh${NC}"
    echo
    echo -e "${BLUE}常见问题:${NC}"
    echo -e "  • 证书申请失败: 检查Cloudflare API Token和DNS配置"
    echo -e "  • Docker启动失败: 确认Docker Compose V2已安装"
    echo -e "  • 服务无法访问: 检查防火墙和端口配置"
    echo
    echo -e "${BLUE}技术支持:${NC}"
    echo -e "  • 查看日志: docker compose logs"
    echo -e "  • 检查状态: docker compose ps"
    echo -e "  • 重启服务: docker compose restart"
    echo
    echo -e "${CYAN}按任意键返回主菜单...${NC}"
    read -n 1
}

# 主循环
main() {
    # 设置脚本权限
    chmod +x "$SCRIPT_DIR/deploy.sh"
    chmod +x "$SCRIPT_DIR/scripts/certificate-manager.sh"
    
    while true; do
        show_main_menu
        read -p "请选择操作 [0-6]: " choice
        
        case $choice in
            1)
                quick_deploy_wizard
                ;;
            2)
                custom_deploy
                ;;
            3)
                manage_certificates
                ;;
            4)
                show_system_status
                ;;
            5)
                system_maintenance
                ;;
            6)
                show_help
                ;;
            0)
                echo -e "${GREEN}感谢使用 Synapse 自动部署系统！${NC}"
                exit 0
                ;;
            *)
                log_error "无效选择，请重试"
                sleep 1
                ;;
        esac
    done
}

# 执行主函数
main "$@"
