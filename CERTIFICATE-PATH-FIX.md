# 证书路径检测问题修复报告

## 🔍 问题描述

**发现时间**: 2025年7月14日  
**问题现象**: 证书申请成功，但设置证书时出现路径错误

### 错误信息
```
[信息] 证书申请成功: niub.one
[错误] 证书目录不存在: /root/.acme.sh/niub.one
[错误] 证书设置失败
```

### 根本原因
acme.sh在申请ECC证书时，会创建带有`_ecc`后缀的目录（如`niub.one_ecc`），但原始脚本只检查不带后缀的目录（如`niub.one`），导致路径检测失败。

## 🛠️ 修复方案

### 1. 智能目录检测
修改`certificate-manager.sh`中的证书目录检测逻辑，支持多种目录格式：

```bash
# 智能检测证书目录（支持ECC和RSA证书）
local cert_dir=""
local possible_dirs=(
    "$ACME_HOME/${domain}_ecc"    # ECC证书目录
    "$ACME_HOME/$domain"          # RSA证书目录
)

for dir in "${possible_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        cert_dir="$dir"
        log_info "找到证书目录: $cert_dir"
        break
    fi
done
```

### 2. 智能文件名映射
改进文件名检测逻辑，支持acme.sh的实际文件命名规则：

```bash
# 创建软链接（智能检测文件名）
local file_mappings=(
    "fullchain.cer:fullchain.pem"
    "${domain}.key:privkey.pem"      # 主文件名
    "${domain}.cer:cert.pem"         # 主文件名
    "ca.cer:chain.pem"
)
```

### 3. 备用文件名支持
为常见的文件名变体提供备用检测：

```bash
# 尝试备用文件名
case "$src_name" in
    "${domain}.key")
        local alt_src="$cert_dir/privkey.key"
        if [[ -f "$alt_src" ]]; then
            # 使用备用文件名创建软链接
        fi
        ;;
esac
```

## ✅ 修复验证

### 语法检查
```bash
bash -n scripts/certificate-manager.sh
✓ 语法检查通过
```

### 包完整性验证
```bash
./verify-package.sh
✓ 39/39 项检查通过
✓ 成功率: 100%
```

## 🔧 技术细节

### acme.sh证书类型和目录结构
- **RSA证书**: 存储在`$ACME_HOME/domain.com/`目录
- **ECC证书**: 存储在`$ACME_HOME/domain.com_ecc/`目录

### 文件名规则
- **证书文件**: `domain.com.cer`
- **私钥文件**: `domain.com.key`
- **完整链**: `fullchain.cer`
- **CA证书**: `ca.cer`

### 软链接目标
- `fullchain.cer` → `fullchain.pem`
- `domain.com.key` → `privkey.pem`
- `domain.com.cer` → `cert.pem`
- `ca.cer` → `chain.pem`

## 📋 测试建议

### 1. ECC证书测试
```bash
# 申请ECC证书
./scripts/certificate-manager.sh apply domain.com

# 验证软链接创建
ls -la ssl/
```

### 2. RSA证书测试
```bash
# 申请RSA证书（如果支持）
./scripts/certificate-manager.sh apply domain.com --rsa

# 验证软链接创建
ls -la ssl/
```

### 3. 路径检测测试
```bash
# 检查证书状态
./scripts/certificate-manager.sh status

# 验证路径检测
./scripts/certificate-manager.sh verify-paths
```

## 🎯 预防措施

### 1. 增强错误信息
修复后的脚本提供更详细的错误信息：
```
[错误] 证书目录不存在，检查的路径：
  - /root/.acme.sh/domain.com_ecc
  - /root/.acme.sh/domain.com
```

### 2. 智能检测日志
添加成功检测的日志信息：
```
[信息] 找到证书目录: /root/.acme.sh/domain.com_ecc
[信息] 已创建软链接: privkey.pem -> /root/.acme.sh/domain.com_ecc/domain.com.key
```

### 3. 备用方案支持
当主文件名不存在时，自动尝试备用文件名并记录：
```
[警告] 源文件不存在: /root/.acme.sh/domain.com_ecc/domain.com.key
[信息] 已创建软链接: privkey.pem -> /root/.acme.sh/domain.com_ecc/privkey.key (备用文件名)
```

## 📚 相关文档更新

### requirement.md更新
已在需求文档中添加相关约束：
- DC-001: 证书路径管理约束
- DC-008: 配置文件自动化约束

### 最佳实践
- 支持多种acme.sh安装位置的自动检测
- 实现智能路径检测算法
- 提供详细的中文错误信息和修复建议

## 🎉 修复成果

1. **兼容性提升**: 支持ECC和RSA两种证书类型
2. **错误处理改进**: 提供详细的错误信息和检测路径
3. **智能检测**: 自动检测多种可能的文件名和目录结构
4. **向后兼容**: 保持对原有证书结构的支持
5. **用户体验**: 更友好的中文错误提示和状态信息

**修复后的证书管理系统现在能够正确处理acme.sh的各种证书类型和目录结构！** ✅
