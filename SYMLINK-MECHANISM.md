# 证书软链接机制说明

## 🔗 软链接机制概述

软链接机制是Synapse自动部署系统的核心功能之一，用于避免重复申请Let's Encrypt证书，确保证书的实时同步和高效管理。

## 🎯 设计目标

### 1. 避免重复申请
- **配额保护**: 防止浪费Let's Encrypt的申请配额
- **智能检测**: 自动检测现有证书的有效期
- **复用机制**: 优先使用现有的有效证书

### 2. 实时同步
- **自动更新**: 证书续期后自动生效，无需手动操作
- **统一管理**: 所有服务使用统一的证书文件位置
- **零停机**: 证书更新不影响服务运行

### 3. 简化部署
- **标准化路径**: 所有证书文件使用标准的nginx路径
- **自动创建**: 部署时自动创建必要的软链接
- **错误恢复**: 自动检测和修复损坏的软链接

## 🏗️ 技术实现

### 证书目录结构
```
internal/
├── ssl/                           # 软链接目录
│   ├── domain.com/               # 基础域名证书
│   │   ├── fullchain.pem         # 完整证书链 (软链接)
│   │   ├── privkey.pem           # 私钥文件 (软链接)
│   │   ├── cert.pem              # 证书文件 (软链接)
│   │   └── chain.pem             # CA证书链 (软链接)
│   ├── matrix.domain.com/        # Matrix域名证书
│   └── turn.domain.com/          # TURN域名证书
```

### acme.sh原始证书位置
```
/root/.acme.sh/
├── domain.com_ecc/               # ECC证书目录
│   ├── fullchain.cer             # 完整证书链
│   ├── domain.com.key            # 私钥文件
│   ├── domain.com.cer            # 证书文件
│   └── ca.cer                    # CA证书
└── domain.com/                   # RSA证书目录 (如果使用)
```

### 软链接映射关系
| 软链接文件 | 源文件 (ECC) | 源文件 (RSA) | 用途 |
|------------|--------------|--------------|------|
| fullchain.pem | fullchain.cer | fullchain.cer | nginx SSL证书 |
| privkey.pem | domain.com.key | domain.com.key | nginx SSL私钥 |
| cert.pem | domain.com.cer | domain.com.cer | 单独证书文件 |
| chain.pem | ca.cer | ca.cer | CA证书链 |

## 🔧 核心功能

### 1. 智能证书检测
```bash
# 检查现有证书（支持ECC和RSA）
local possible_cert_paths=(
    "$ACME_HOME/${domain}_ecc/fullchain.cer"    # ECC证书
    "$ACME_HOME/$domain/fullchain.cer"          # RSA证书
)

for path in "${possible_cert_paths[@]}"; do
    if [[ -f "$path" ]]; then
        cert_path="$path"
        log_info "找到现有证书: $cert_path"
        break
    fi
done
```

### 2. 软链接创建
```bash
# 智能文件名映射
local file_mappings=(
    "fullchain.cer:fullchain.pem"
    "${domain}.key:privkey.pem"
    "${domain}.cer:cert.pem"
    "ca.cer:chain.pem"
)

# 创建软链接
ln -sf "$src_file" "$dst_file"
```

### 3. 软链接验证
```bash
# 检查软链接状态
ensure_symlinks() {
    local domain="$1"
    local key_files=("fullchain.pem" "privkey.pem")
    
    for file in "${key_files[@]}"; do
        if [[ ! -L "$link_path" ]] || [[ ! -f "$link_path" ]]; then
            log_warn "软链接缺失或无效: $file"
            create_symlinks "$domain"
        fi
    done
}
```

## 🚀 使用方法

### 1. 智能证书管理（推荐）
```bash
# 自动检测证书状态，创建软链接，避免重复申请
./scripts/certificate-manager.sh smart
```

### 2. 手动创建软链接
```bash
# 为所有域名创建软链接
./scripts/certificate-manager.sh symlinks

# 检查并修复软链接
./scripts/certificate-manager.sh check-links
```

### 3. 查看证书状态
```bash
# 显示证书状态和软链接信息
./scripts/certificate-manager.sh status
```

## 🔍 工作流程

### 证书申请流程
1. **检测现有证书**: 智能检测ECC和RSA证书
2. **验证有效期**: 检查证书剩余有效期
3. **决策逻辑**:
   - 有效期 > 30天: 跳过申请，创建软链接
   - 有效期 ≤ 30天: 提示用户确认是否申请
   - 无证书: 直接申请新证书
4. **创建软链接**: 申请成功或跳过申请后都创建软链接

### 软链接管理流程
1. **目录检查**: 确保ssl目录存在
2. **文件检测**: 检查源证书文件是否存在
3. **链接创建**: 创建指向源文件的软链接
4. **状态验证**: 验证软链接的有效性
5. **错误恢复**: 自动修复损坏的软链接

## 📊 优势对比

### 传统方式 vs 软链接机制

| 特性 | 传统复制方式 | 软链接机制 |
|------|-------------|------------|
| 证书更新 | 需要手动复制 | 自动同步 |
| 存储空间 | 重复存储 | 节省空间 |
| 配额保护 | 容易重复申请 | 智能避免 |
| 维护成本 | 高 | 低 |
| 错误风险 | 高 | 低 |
| 实时性 | 延迟 | 实时 |

## 🛠️ 故障排除

### 常见问题

#### 1. 软链接不存在
```bash
# 症状: nginx启动失败，提示证书文件不存在
# 解决: 检查并创建软链接
./scripts/certificate-manager.sh check-links
```

#### 2. 软链接指向无效文件
```bash
# 症状: 软链接存在但目标文件不存在
# 解决: 重新创建软链接
./scripts/certificate-manager.sh symlinks
```

#### 3. 证书路径检测失败
```bash
# 症状: 提示找不到证书目录
# 解决: 检查acme.sh安装和证书申请状态
./scripts/certificate-manager.sh status
```

### 调试命令
```bash
# 查看软链接详情
ls -la internal/ssl/*/

# 检查源文件是否存在
ls -la /root/.acme.sh/*/

# 验证软链接有效性
file internal/ssl/*/fullchain.pem
```

## 🎯 最佳实践

### 1. 定期检查
- 建议每月运行一次`check-links`命令
- 在系统维护时验证软链接状态
- 证书续期后检查软链接是否正常

### 2. 备份策略
- 软链接本身不需要备份
- 重点备份acme.sh的证书目录
- 记录证书申请的配置信息

### 3. 监控告警
- 监控证书有效期
- 检查软链接的完整性
- 设置证书到期提醒

## 🔒 安全考虑

### 文件权限
- 证书文件: 644 (可读)
- 私钥文件: 600 (仅所有者可读)
- 软链接: 继承目标文件权限

### 访问控制
- 限制对acme.sh目录的访问
- 确保nginx用户可以读取证书文件
- 定期审查文件权限设置

## 📈 性能优化

### 减少I/O操作
- 软链接避免文件复制
- 减少磁盘空间占用
- 提高文件访问速度

### 自动化程度
- 减少手动操作
- 降低人为错误
- 提高系统可靠性

---

**软链接机制是确保证书管理高效、可靠的核心技术，正确使用可以大大简化证书维护工作！** 🎉
