# SSL证书管理指南

本指南详细介绍了Synapse自动部署系统中的SSL证书管理功能，包括智能证书管理、软链接机制和Let's Encrypt配额保护。

## 🔐 证书管理概述

### 核心特性
- **智能配额保护**: 避免不必要的Let's Encrypt证书申请
- **软链接机制**: 实现证书文件的实时同步
- **多域名支持**: 独立管理基础域名、Matrix域名、TURN域名
- **自动续期**: 定时检查和续期即将过期的证书
- **路径自动检测**: 支持多种acme.sh安装位置

### 证书架构
```
项目根目录/
├── internal/
│   ├── ssl/                    # 证书软链接目录
│   │   ├── example.com/
│   │   │   ├── fullchain.pem   # 软链接到acme.sh证书
│   │   │   ├── privkey.pem     # 软链接到acme.sh私钥
│   │   │   ├── cert.pem        # 软链接到证书文件
│   │   │   └── chain.pem       # 软链接到证书链
│   │   ├── matrix.example.com/
│   │   └── turn.example.com/
│   └── scripts/
│       └── certificate-manager.sh
└── ~/.acme.sh/                 # acme.sh实际证书存储
    ├── example.com/
    ├── matrix.example.com/
    └── turn.example.com/
```

## 🚀 快速开始

### 基本使用
```bash
# 进入脚本目录
cd internal/scripts

# 智能证书管理（推荐）
./certificate-manager.sh smart

# 检查证书状态
./certificate-manager.sh status

# 显示帮助信息
./certificate-manager.sh help
```

### 首次设置
```bash
# 1. 确保配置文件正确
cat ../../config.env | grep -E "(DOMAIN_NAME|CLOUDFLARE_API_TOKEN|CERT_EMAIL)"

# 2. 安装acme.sh（如果未安装）
./certificate-manager.sh install

# 3. 申请所有证书
./certificate-manager.sh smart
```

## 📋 详细功能说明

### 1. 智能证书管理
```bash
./certificate-manager.sh smart
```

**功能说明**:
- 自动检测现有证书状态
- 只申请必要的证书（有效期<30天）
- 自动创建软链接
- 支持多域名批量处理

**执行流程**:
1. 检测acme.sh安装路径
2. 检查每个域名的证书有效期
3. 跳过有效期>30天的证书
4. 申请需要的证书
5. 创建软链接到项目目录

### 2. 证书状态检查
```bash
./certificate-manager.sh status
```

**显示信息**:
- 每个域名的证书有效期
- 证书文件路径
- 软链接状态
- acme.sh安装状态

**示例输出**:
```
================================
        证书状态报告
================================

域名: example.com
  状态: 有效
  剩余天数: 45 天
  证书路径: /home/<USER>/.acme.sh/example.com/fullchain.cer
  软链接目录: /path/to/project/internal/ssl/example.com
    ✓ fullchain.pem -> /home/<USER>/.acme.sh/example.com/fullchain.cer
    ✓ privkey.pem -> /home/<USER>/.acme.sh/example.com/example.com.key
```

### 3. 手动证书申请
```bash
# 申请单个域名证书
./certificate-manager.sh issue example.com

# 强制重新申请（谨慎使用）
./certificate-manager.sh force example.com
```

**注意事项**:
- `issue`命令会检查现有证书，避免重复申请
- `force`命令会强制申请，消耗Let's Encrypt配额
- 强制申请需要用户确认

### 4. 软链接管理
```bash
# 为所有域名创建软链接
./certificate-manager.sh symlinks
```

**软链接优势**:
- 证书更新后自动生效，无需重启服务
- 避免证书文件重复存储
- 简化证书路径管理
- 支持acme.sh的自动续期

### 5. acme.sh安装
```bash
./certificate-manager.sh install
```

**安装特性**:
- 自动下载最新版本
- 配置邮箱通知
- 支持多种安装位置检测

## ⚙️ 高级配置

### 配置参数
在`config.env`中配置以下参数：

```bash
# 证书相关配置
CLOUDFLARE_API_TOKEN=your_token_here    # Cloudflare API Token
CERT_EMAIL=<EMAIL>            # 证书通知邮箱
CERT_THRESHOLD_DAYS=30                   # 证书续期阈值（天）

# 域名配置
DOMAIN_NAME=example.com                  # 基础域名
MATRIX_DOMAIN=matrix.example.com         # Matrix服务域名
TURN_DOMAIN=turn.example.com             # TURN服务域名
```

### Cloudflare API Token配置
1. 访问 https://dash.cloudflare.com/profile/api-tokens
2. 创建自定义Token
3. 权限设置：
   - Zone:Zone:Read
   - Zone:DNS:Edit
4. 区域资源：包含您的域名

### 证书续期策略
- **自动检查**: 每次运行smart命令时检查
- **阈值控制**: 默认30天内到期才续期
- **配额保护**: 避免频繁申请浪费配额
- **软链接更新**: 续期后自动更新软链接

## 🔧 故障排除

### 常见问题

#### 1. acme.sh未找到
**错误**: `未找到 acme.sh 安装路径`
**解决**:
```bash
# 检查常见安装位置
ls -la ~/.acme.sh/
ls -la /root/.acme.sh/

# 重新安装
./certificate-manager.sh install

# 手动安装
curl https://get.acme.sh | sh -s email=<EMAIL>
```

#### 2. Cloudflare API错误
**错误**: `DNS challenge failed`
**解决**:
```bash
# 验证API Token
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
     -H "Authorization: Bearer YOUR_API_TOKEN"

# 检查域名是否在Cloudflare
curl -X GET "https://api.cloudflare.com/client/v4/zones?name=example.com" \
     -H "Authorization: Bearer YOUR_API_TOKEN"

# 重新设置API Token
export CF_Token="your_new_token"
./certificate-manager.sh force example.com
```

#### 3. 软链接失效
**错误**: `软链接目标不存在`
**解决**:
```bash
# 检查软链接状态
find internal/ssl/ -type l -exec test ! -e {} \; -print

# 重新创建软链接
./certificate-manager.sh symlinks

# 手动修复
cd internal/ssl/example.com/
rm -f *.pem
ln -sf ~/.acme.sh/example.com/fullchain.cer fullchain.pem
ln -sf ~/.acme.sh/example.com/example.com.key privkey.pem
```

#### 4. 权限问题
**错误**: `Permission denied`
**解决**:
```bash
# 检查文件权限
ls -la ~/.acme.sh/example.com/

# 修复权限
chmod 644 ~/.acme.sh/example.com/*.cer
chmod 600 ~/.acme.sh/example.com/*.key

# 检查目录权限
chmod 755 ~/.acme.sh/example.com/
```

### 调试命令
```bash
# 详细日志模式
export DEBUG=1
./certificate-manager.sh smart

# 检查acme.sh日志
cat ~/.acme.sh/acme.sh.log

# 手动测试DNS API
~/.acme.sh/acme.sh --issue --dns dns_cf -d example.com --debug
```

## 📅 维护建议

### 定期检查
```bash
# 每周检查证书状态
./certificate-manager.sh status

# 每月运行智能管理
./certificate-manager.sh smart
```

### 自动化维护
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点检查证书
0 2 * * * /path/to/project/internal/scripts/certificate-manager.sh smart

# 每周发送状态报告
0 8 * * 1 /path/to/project/internal/scripts/certificate-manager.sh status | mail -s "证书状态报告" <EMAIL>
```

### 备份策略
```bash
# 备份acme.sh目录
tar -czf acme_backup_$(date +%Y%m%d).tar.gz ~/.acme.sh/

# 备份项目SSL目录
tar -czf ssl_backup_$(date +%Y%m%d).tar.gz internal/ssl/
```

## 🔒 安全最佳实践

### API Token安全
- 使用最小权限原则
- 定期轮换API Token
- 不要在日志中记录Token
- 使用环境变量存储敏感信息

### 证书安全
- 定期检查证书有效期
- 监控证书申请日志
- 备份证书和私钥
- 使用强密码保护私钥

### 访问控制
- 限制证书文件访问权限
- 定期审查用户权限
- 监控证书相关操作
- 使用防火墙保护API访问

---

**提示**: 证书管理是系统安全的关键组成部分。建议定期检查证书状态，并保持acme.sh和相关工具的最新版本。
