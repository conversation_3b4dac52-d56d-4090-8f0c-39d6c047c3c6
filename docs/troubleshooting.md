# Synapse 故障排除指南

本指南提供了Matrix Synapse自动部署系统常见问题的详细解决方案。

## 🔍 诊断工具

### 快速诊断
```bash
# 运行健康检查
cd internal/scripts
./health-check.sh

# 检查系统状态
cd internal
docker compose ps
docker compose logs --tail=50
```

### 详细诊断
```bash
# 检查配置文件
docker compose config

# 检查网络连接
curl -f http://localhost:8008/health
curl -f https://localhost:443

# 检查证书状态
./scripts/certificate-manager.sh status
```

## 🚨 常见问题解决

### 1. Docker相关问题

#### 问题: Docker Compose命令不存在
**错误信息**: `docker-compose: command not found`
**原因**: 使用了旧版本的docker-compose命令
**解决方案**:
```bash
# 检查Docker Compose V2
docker compose version

# 如果不存在，安装Docker Compose V2
sudo apt update
sudo apt install docker-compose-plugin

# 或更新Docker
curl -fsSL https://get.docker.com | sh
```

#### 问题: 容器启动失败
**错误信息**: `Container exited with code 1`
**诊断步骤**:
```bash
# 查看详细日志
docker compose logs synapse
docker compose logs nginx
docker compose logs coturn

# 检查端口占用
sudo netstat -tlnp | grep -E ':(80|443|8008|3478)'

# 检查配置文件语法
nginx -t
docker compose config
```

**常见解决方案**:
```bash
# 端口被占用
sudo systemctl stop apache2  # 如果安装了Apache
sudo systemctl stop nginx    # 如果安装了系统Nginx

# 权限问题
sudo chown -R $USER:$USER internal/
chmod +x internal/scripts/*.sh

# 重新生成配置
cd internal
rm -f homeserver.yaml nginx.conf coturn.conf
../deploy.sh  # 重新运行部署
```

### 2. SSL证书问题

#### 问题: 证书申请失败
**错误信息**: `acme.sh: command not found` 或 `DNS challenge failed`
**诊断步骤**:
```bash
# 检查acme.sh安装
ls -la ~/.acme.sh/
which acme.sh

# 检查Cloudflare API
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
     -H "Authorization: Bearer YOUR_API_TOKEN"

# 检查DNS解析
nslookup your-domain.com
dig your-domain.com
```

**解决方案**:
```bash
# 重新安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>
source ~/.bashrc

# 验证Cloudflare API Token权限
# 需要Zone:Zone:Read, Zone:DNS:Edit权限

# 手动申请证书
cd internal/scripts
export CF_Token="your_cloudflare_api_token"
./certificate-manager.sh force your-domain.com
```

#### 问题: 证书软链接失效
**错误信息**: `SSL certificate not found`
**解决方案**:
```bash
# 重新创建软链接
cd internal/scripts
./certificate-manager.sh symlinks

# 检查软链接状态
ls -la ../ssl/*/
find ../ssl/ -type l -exec test ! -e {} \; -print  # 查找失效链接

# 手动修复软链接
cd internal/ssl/your-domain.com/
ln -sf ~/.acme.sh/your-domain.com/fullchain.cer fullchain.pem
ln -sf ~/.acme.sh/your-domain.com/your-domain.com.key privkey.pem
```

### 3. 网络连接问题

#### 问题: 服务无法访问
**错误信息**: `Connection refused` 或 `Timeout`
**诊断步骤**:
```bash
# 检查服务状态
docker compose ps

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|443|8008|3478)'

# 检查防火墙
sudo ufw status
sudo iptables -L

# 测试内部连接
curl -f http://localhost:8008/health
curl -f https://localhost:443
```

**解决方案**:
```bash
# 开放防火墙端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3478/udp
sudo ufw allow 5349/tcp

# 检查ISP端口阻塞
# 如果80/443被阻塞，修改config.env
HTTPS_PORT=8443
HTTP_PORT=8080

# 重新生成配置并重启
cd internal
docker compose down
../deploy.sh
```

#### 问题: Well-known端点无法访问
**错误信息**: `404 Not Found` 访问 `/.well-known/matrix/server`
**解决方案**:
```bash
# 检查Nginx配置
docker compose exec nginx nginx -t

# 测试Well-known端点
curl -f https://your-domain.com/.well-known/matrix/server
curl -f https://your-domain.com/.well-known/matrix/client

# 重新生成Nginx配置
cd internal
source ../config.env
sed -e "s/DOMAIN_NAME/$DOMAIN_NAME/g" \
    -e "s/MATRIX_DOMAIN/$MATRIX_DOMAIN/g" \
    -e "s/HTTPS_PORT/${HTTPS_PORT:-443}/g" \
    configs/nginx.conf.template > nginx.conf

# 重启Nginx
docker compose restart nginx
```

### 4. Synapse服务问题

#### 问题: Synapse启动失败
**错误信息**: `KeyError: 'server_name'` 或 `Database error`
**诊断步骤**:
```bash
# 检查Synapse日志
docker compose logs synapse

# 检查配置文件
docker compose exec synapse cat /data/homeserver.yaml

# 检查数据库
docker compose exec synapse ls -la /data/
```

**解决方案**:
```bash
# 重新生成配置
cd internal
source ../config.env
sed -e "s/SYNAPSE_SERVER_NAME/$MATRIX_DOMAIN/g" \
    -e "s/SYNAPSE_REPORT_STATS/${SYNAPSE_REPORT_STATS:-no}/g" \
    configs/homeserver.yaml.template > homeserver.yaml

# 检查环境变量
docker compose exec synapse env | grep SYNAPSE

# 重新初始化数据库
docker compose down
rm -f data/homeserver.db*
docker compose up -d
```

#### 问题: 用户无法注册或登录
**错误信息**: `Registration is disabled` 或 `Invalid credentials`
**解决方案**:
```bash
# 检查注册设置
docker compose exec synapse grep -A5 "enable_registration" /data/homeserver.yaml

# 创建用户
cd internal/scripts
./user-manager.sh

# 或手动创建
docker compose exec synapse register_new_matrix_user \
    -u username -p password -a -c /data/homeserver.yaml http://localhost:8008

# 重置密码
docker compose exec synapse synapse_admin reset-password \
    "@username:your-domain.com" "new_password"
```

### 5. RouterOS集成问题

#### 问题: Python脚本执行失败（Debian 12）
**错误信息**: `externally-managed-environment`
**解决方案**:
```bash
# 创建虚拟环境
cd internal/routeros
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行脚本
python3 ip-monitor.py

# 或使用系统包
sudo apt install python3-requests python3-routeros-api --break-system-packages
```

#### 问题: RouterOS API连接失败
**错误信息**: `Connection refused` 或 `Authentication failed`
**解决方案**:
```bash
# 检查RouterOS API配置
# 在RouterOS中启用REST API:
# /ip service enable api
# /ip service set api port=8728

# 测试连接
curl -u username:password http://routeros-ip:8728/rest/system/identity

# 检查防火墙规则
# 确保RouterOS允许API访问
```

### 6. 性能问题

#### 问题: 服务响应缓慢
**诊断步骤**:
```bash
# 检查系统资源
./scripts/health-check.sh

# 检查容器资源使用
docker stats

# 检查数据库大小
du -sh internal/data/homeserver.db
```

**解决方案**:
```bash
# 增加系统资源
# 考虑升级服务器配置

# 优化数据库
docker compose exec synapse sqlite3 /data/homeserver.db "VACUUM;"

# 清理日志
docker compose exec synapse find /data -name "*.log*" -mtime +7 -delete

# 重启服务
docker compose restart
```

## 🔧 维护命令

### 日常维护
```bash
# 健康检查
./scripts/health-check.sh

# 备份数据
./scripts/backup.sh backup

# 查看日志
docker compose logs --tail=100 -f

# 重启服务
docker compose restart
```

### 紧急恢复
```bash
# 完全重启
docker compose down
docker compose up -d

# 恢复备份
./scripts/backup.sh list
# 手动恢复备份文件

# 重新部署
./deploy.sh
```

## 📞 获取支持

### 日志收集
在寻求支持时，请提供以下信息：
```bash
# 系统信息
uname -a
docker --version
docker compose version

# 服务状态
docker compose ps
docker compose logs --tail=100

# 健康检查结果
./scripts/health-check.sh

# 配置信息（隐藏敏感信息）
cat config.env | grep -v -E "(TOKEN|PASSWORD|SECRET)"
```

### 联系方式
- 查看项目文档
- 提交Issue到项目仓库
- 参考官方Matrix文档

---

**提示**: 大多数问题可以通过重新运行部署脚本解决。如果问题持续存在，请收集详细的日志信息并寻求技术支持。
