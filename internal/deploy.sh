#!/bin/bash

# Synapse自动部署系统 - 主部署脚本
# 实现一键部署功能和中文用户界面

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 配置文件路径（优先使用当前目录的配置）
if [[ -f "$SCRIPT_DIR/config.env" ]]; then
    CONFIG_FILE="$SCRIPT_DIR/config.env"
elif [[ -f "$SCRIPT_DIR/../config.env" ]]; then
    CONFIG_FILE="$SCRIPT_DIR/../config.env"
else
    CONFIG_FILE="$SCRIPT_DIR/config.env"
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 自动部署系统${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "${GREEN}版本: 1.0.0${NC}"
    echo -e "${GREEN}基于: Matrix Synapse v1.110+${NC}"
    echo -e "${GREEN}支持: Docker Compose V2${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求"
    
    # 检查操作系统（跨平台兼容）
    local os_name
    if [[ "$(uname)" == "Darwin" ]]; then
        os_name="macOS $(sw_vers -productVersion)"
        log_info "操作系统: $os_name"
    elif [[ -f /etc/os-release ]]; then
        source /etc/os-release
        log_info "操作系统: $PRETTY_NAME"
    else
        os_name="$(uname -s) $(uname -r)"
        log_info "操作系统: $os_name"
    fi
    
    # 检查支持的系统
    if [[ "$(uname)" == "Darwin" ]]; then
        log_warn "在macOS上运行，某些功能可能需要调整"
    elif [[ -n "${ID:-}" ]]; then
        case "$ID" in
            ubuntu|debian)
                log_info "支持的操作系统: $ID"
                ;;
            *)
                log_warn "未测试的操作系统: $ID，可能存在兼容性问题"
                ;;
        esac
    else
        log_warn "无法确定操作系统类型，继续执行"
    fi
    
    # 检查Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_warn "Docker 未安装，正在安装..."
        install_docker
    else
        log_info "Docker 已安装: $(docker --version)"
    fi
    
    # 检查Docker Compose V2
    if ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose V2 未安装或不可用"
        log_info "请安装 Docker Compose V2 或更新 Docker"
        exit 1
    else
        log_info "Docker Compose: $(docker compose version)"
    fi
    
    # 检查Python
    if ! command -v python3 >/dev/null 2>&1; then
        log_warn "Python3 未安装，正在安装..."
        install_python
    else
        log_info "Python3 已安装: $(python3 --version)"
    fi
    
    # 检查其他依赖
    local deps=("curl" "openssl" "git")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            log_warn "$dep 未安装，正在安装..."
            install_package "$dep"
        else
            log_info "$dep 已安装"
        fi
    done
}

# 安装Docker
install_docker() {
    log_info "正在安装 Docker..."
    
    # 更新包索引
    sudo apt-get update
    
    # 安装依赖
    sudo apt-get install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # 添加Docker官方GPG密钥
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/$ID/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    # 添加Docker仓库
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/$ID \
        $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 启动Docker服务
    sudo systemctl enable docker
    sudo systemctl start docker
    
    # 添加用户到docker组
    sudo usermod -aG docker "$USER"
    
    log_info "Docker 安装完成，请重新登录以使用Docker"
}

# 安装Python
install_python() {
    log_info "正在安装 Python3..."
    sudo apt-get update
    sudo apt-get install -y python3 python3-pip python3-venv
}

# 安装软件包
install_package() {
    local package="$1"
    log_info "正在安装 $package..."
    sudo apt-get update
    sudo apt-get install -y "$package"
}

# 加载配置
load_config() {
    log_step "加载配置文件"

    # 自动创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件不存在，正在创建默认配置文件..."

        if [[ -f "$SCRIPT_DIR/config-example.env" ]]; then
            cp "$SCRIPT_DIR/config-example.env" "$CONFIG_FILE"
            chmod 600 "$CONFIG_FILE"
            log_info "已创建配置文件: $CONFIG_FILE"
            log_warn "请编辑配置文件设置您的域名和API密钥："
            log_info "  nano $CONFIG_FILE"
            log_info "必需配置项："
            log_info "  - DOMAIN_NAME (您的域名)"
            log_info "  - CLOUDFLARE_API_TOKEN (Cloudflare API密钥)"
            log_info "  - CERT_EMAIL (证书通知邮箱)"
            log_info "  - ADMIN_USERNAME 和 ADMIN_PASSWORD (管理员账户)"
            echo
            read -p "配置完成后按任意键继续，或按Ctrl+C退出..." -n 1
            echo
        else
            log_error "配置示例文件不存在: $SCRIPT_DIR/config-example.env"
            exit 1
        fi
    fi
    
    # 安全地加载配置文件
    while IFS='=' read -r key value; do
        if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
            value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
            export "$key"="$value"
        fi
    done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')
    
    # 验证必需的配置
    local required_vars=("DOMAIN_NAME" "CLOUDFLARE_API_TOKEN" "CERT_EMAIL")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "缺少必需的配置参数: $var"
            log_info "请检查 config.env 中的配置"
            exit 1
        fi
    done
    
    # 设置默认值
    MATRIX_DOMAIN="${MATRIX_DOMAIN:-matrix.$DOMAIN_NAME}"
    TURN_DOMAIN="${TURN_DOMAIN:-turn.$DOMAIN_NAME}"
    HTTPS_PORT="${HTTPS_PORT:-443}"
    HTTP_PORT="${HTTP_PORT:-80}"
    SYNAPSE_PORT="${SYNAPSE_PORT:-8008}"
    TURN_PORT="${TURN_PORT:-3478}"
    SYNAPSE_REPORT_STATS="${SYNAPSE_REPORT_STATS:-no}"

    # 处理动态生成的密码
    if [[ "$POSTGRES_PASSWORD" == '$(openssl rand -hex 16)' ]]; then
        POSTGRES_PASSWORD=$(openssl rand -hex 16)
        log_info "已自动生成PostgreSQL密码"
    fi

    # 解析包含变量引用的配置
    MATRIX_DOMAIN=$(echo "$MATRIX_DOMAIN" | envsubst)
    TURN_DOMAIN=$(echo "$TURN_DOMAIN" | envsubst)
    CERT_EMAIL=$(echo "$CERT_EMAIL" | envsubst)
    SYNAPSE_SERVER_NAME=$(echo "$SYNAPSE_SERVER_NAME" | envsubst)
    
    log_info "配置加载完成"
    log_info "基础域名: $DOMAIN_NAME"
    log_info "Matrix域名: $MATRIX_DOMAIN"
    log_info "TURN域名: $TURN_DOMAIN"
}

# 生成随机密钥
generate_secrets() {
    log_step "生成安全密钥"
    
    # 生成注册密钥
    if [[ -z "${REGISTRATION_SHARED_SECRET:-}" ]]; then
        REGISTRATION_SHARED_SECRET=$(openssl rand -hex 32)
        log_info "已生成注册共享密钥"
    fi
    
    # 生成TURN密钥
    if [[ -z "${TURN_SHARED_SECRET:-}" ]]; then
        TURN_SHARED_SECRET=$(openssl rand -hex 32)
        log_info "已生成TURN共享密钥"
    fi
    
    # 保存密钥到配置文件
    if ! grep -q "REGISTRATION_SHARED_SECRET" "$CONFIG_FILE"; then
        echo "REGISTRATION_SHARED_SECRET=$REGISTRATION_SHARED_SECRET" >> "$CONFIG_FILE"
    fi
    
    if ! grep -q "TURN_SHARED_SECRET" "$CONFIG_FILE"; then
        echo "TURN_SHARED_SECRET=$TURN_SHARED_SECRET" >> "$CONFIG_FILE"
    fi
}

# 处理配置文件
process_configs() {
    log_step "处理配置文件"
    
    # 创建必要的目录
    mkdir -p "$SCRIPT_DIR/data" "$SCRIPT_DIR/ssl" "$SCRIPT_DIR/logs"
    
    # 处理Synapse配置
    log_info "生成 Synapse 配置文件..."

    # 设置默认值
    local enable_registration="${ENABLE_REGISTRATION:-true}"
    local enable_federation="${ENABLE_FEDERATION:-true}"
    local registration_type="${REGISTRATION_TYPE:-invite_only}"
    local synapse_report_stats="${ENABLE_STATS_REPORTING:-false}"
    local database_type="${DATABASE_TYPE:-sqlite}"

    # 生成数据库配置
    local database_config=""
    if [[ "$database_type" == "postgresql" ]]; then
        database_config="database:
  name: psycopg2
  args:
    user: ${POSTGRES_USER:-synapse}
    password: ${POSTGRES_PASSWORD}
    database: ${POSTGRES_DB:-synapse}
    host: ${POSTGRES_HOST:-postgres}
    port: ${POSTGRES_PORT:-5432}
    cp_min: 5
    cp_max: 10"
    else
        database_config="database:
  name: sqlite3
  args:
    database: /data/homeserver.db
    # SQLite性能优化
    cp_min: 5
    cp_max: 10
    cp_noisy: false"
    fi

    # 替换配置模板
    sed -e "s/SYNAPSE_SERVER_NAME/$SYNAPSE_SERVER_NAME/g" \
        -e "s/SYNAPSE_REPORT_STATS/$synapse_report_stats/g" \
        -e "s/TURN_DOMAIN/$TURN_DOMAIN/g" \
        -e "s/REGISTRATION_SHARED_SECRET/$REGISTRATION_SHARED_SECRET/g" \
        -e "s/TURN_SHARED_SECRET/$TURN_SHARED_SECRET/g" \
        -e "s/CERT_EMAIL/$CERT_EMAIL/g" \
        -e "s/ENABLE_REGISTRATION/$enable_registration/g" \
        -e "s/ENABLE_FEDERATION/$enable_federation/g" \
        -e "s/REGISTRATION_TYPE/$registration_type/g" \
        "$SCRIPT_DIR/configs/homeserver.yaml.template" > "$SCRIPT_DIR/homeserver.yaml.tmp"

    # 替换数据库配置（使用临时文件避免awk多行问题）
    echo "$database_config" > "$SCRIPT_DIR/database_config.tmp"
    awk '{
        if ($0 == "DATABASE_CONFIG") {
            while ((getline line < "'$SCRIPT_DIR'/database_config.tmp") > 0) {
                print line
            }
            close("'$SCRIPT_DIR'/database_config.tmp")
        } else {
            print $0
        }
    }' "$SCRIPT_DIR/homeserver.yaml.tmp" > "$SCRIPT_DIR/homeserver.yaml"
    rm -f "$SCRIPT_DIR/database_config.tmp"

    rm -f "$SCRIPT_DIR/homeserver.yaml.tmp"
    
    # 处理Nginx配置
    log_info "生成 Nginx 配置文件..."
    sed -e "s/DOMAIN_NAME/$DOMAIN_NAME/g" \
        -e "s/MATRIX_DOMAIN/$MATRIX_DOMAIN/g" \
        -e "s/HTTPS_PORT/$HTTPS_PORT/g" \
        "$SCRIPT_DIR/configs/nginx.conf.template" > "$SCRIPT_DIR/nginx.conf"
    
    # 处理Coturn配置
    log_info "生成 Coturn 配置文件..."
    # 获取外部IP（简单实现）
    EXTERNAL_IP=$(curl -s ifconfig.me || echo "127.0.0.1")

    # 设置TURN UDP端口范围默认值
    local turn_udp_min="${TURN_UDP_MIN_PORT:-65335}"
    local turn_udp_max="${TURN_UDP_MAX_PORT:-65535}"

    sed -e "s/TURN_DOMAIN/$TURN_DOMAIN/g" \
        -e "s/TURN_SHARED_SECRET/$TURN_SHARED_SECRET/g" \
        -e "s/EXTERNAL_IP/$EXTERNAL_IP/g" \
        -e "s/TURN_UDP_MIN_PORT/$turn_udp_min/g" \
        -e "s/TURN_UDP_MAX_PORT/$turn_udp_max/g" \
        "$SCRIPT_DIR/configs/coturn.conf.template" > "$SCRIPT_DIR/coturn.conf"
    
    # 处理日志配置
    log_info "生成日志配置文件..."
    cp "$SCRIPT_DIR/configs/log.config.template" "$SCRIPT_DIR/log.config"
    
    log_info "配置文件生成完成"
}

# 申请SSL证书
setup_certificates() {
    log_step "设置SSL证书"

    log_info "开始智能证书管理..."
    if "$SCRIPT_DIR/scripts/certificate-manager.sh" smart; then
        log_info "证书设置完成"
    else
        log_warn "证书设置遇到问题，但将继续部署"
        log_info "您可以稍后手动运行: ./scripts/certificate-manager.sh smart"

        # 检查是否至少有一个域名的证书文件存在
        local cert_exists=false
        for domain in "$DOMAIN_NAME" "$MATRIX_DOMAIN" "$TURN_DOMAIN"; do
            if [[ -f "$SCRIPT_DIR/ssl/$domain/fullchain.pem" ]]; then
                cert_exists=true
                log_info "发现域名 $domain 的证书文件，继续部署"
                break
            fi
        done

        if [[ "$cert_exists" == "false" ]]; then
            log_error "未找到任何有效的证书文件，部署无法继续"
            log_info "请检查域名DNS设置和Cloudflare API配置"
            exit 1
        fi
    fi
}

# 启动服务
start_services() {
    log_step "启动Docker服务"
    
    cd "$SCRIPT_DIR"
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker compose down 2>/dev/null || true
    
    # 根据数据库类型启动服务
    local database_type="${DATABASE_TYPE:-sqlite}"
    if [[ "$database_type" == "postgresql" ]]; then
        log_info "启动PostgreSQL数据库服务..."
        if docker compose --profile postgres up -d; then
            log_info "服务启动成功"
        else
            log_error "服务启动失败"
            exit 1
        fi
    else
        log_info "启动SQLite模式服务..."
        if docker compose up -d synapse nginx coturn; then
            log_info "服务启动成功"
        else
            log_error "服务启动失败"
            exit 1
        fi
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    docker compose ps
}

# 创建管理员用户
create_admin_user() {
    log_step "创建管理员用户"
    
    if [[ -n "${ADMIN_USERNAME:-}" ]] && [[ -n "${ADMIN_PASSWORD:-}" ]]; then
        log_info "正在创建管理员用户: $ADMIN_USERNAME"
        
        # 等待Synapse完全启动
        sleep 20
        
        # 创建用户
        docker compose exec synapse register_new_matrix_user \
            -u "$ADMIN_USERNAME" \
            -p "$ADMIN_PASSWORD" \
            -a \
            -c /data/homeserver.yaml \
            http://localhost:8008
        
        log_info "管理员用户创建完成"
    else
        log_warn "未配置管理员用户信息，跳过创建"
    fi
}

# 显示部署结果
show_deployment_result() {
    log_step "部署完成"
    
    echo -e "${GREEN}🎉 Synapse 部署成功！${NC}"
    echo
    echo -e "${CYAN}访问信息:${NC}"
    echo -e "  Matrix服务器: ${WHITE}https://$MATRIX_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "  服务器名称: ${WHITE}$MATRIX_DOMAIN${NC}"
    echo
    echo -e "${CYAN}客户端配置:${NC}"
    echo -e "  推荐客户端: ${WHITE}Element (https://app.element.io)${NC}"
    echo -e "  服务器地址: ${WHITE}https://$MATRIX_DOMAIN:$HTTPS_PORT${NC}"
    echo
    if [[ -n "${ADMIN_USERNAME:-}" ]]; then
        echo -e "${CYAN}管理员账户:${NC}"
        echo -e "  用户名: ${WHITE}@$ADMIN_USERNAME:$MATRIX_DOMAIN${NC}"
        echo -e "  密码: ${WHITE}$ADMIN_PASSWORD${NC}"
        echo
    fi
    echo -e "${CYAN}管理命令:${NC}"
    echo -e "  查看状态: ${WHITE}docker compose ps${NC}"
    echo -e "  查看日志: ${WHITE}docker compose logs -f${NC}"
    echo -e "  重启服务: ${WHITE}docker compose restart${NC}"
    echo -e "  停止服务: ${WHITE}docker compose down${NC}"
    echo
    echo -e "${YELLOW}注意: 首次启动可能需要几分钟时间来初始化数据库${NC}"
}

# 主函数
main() {
    show_banner

    # 首先加载环境变量（如果存在.env文件）
    if [[ -f "$SCRIPT_DIR/.env" ]]; then
        log_info "加载环境变量文件..."
        source "$SCRIPT_DIR/.env"
    fi

    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        log_warn "不建议以root权限运行此脚本"
        echo -e "${YELLOW}是否继续？${NC}"
        echo -e "${RED}[0]${NC} 退出"
        echo -e "${GREEN}[1]${NC} 继续"
        read -p "请选择 [0-1]: " choice
        case $choice in
            1) ;;
            *) exit 0 ;;
        esac
    fi
    
    # 执行部署步骤
    check_system_requirements
    load_config
    generate_secrets
    process_configs
    setup_certificates
    start_services
    create_admin_user
    show_deployment_result
    
    log_info "部署脚本执行完成"
}

# 执行主函数
main "$@"
