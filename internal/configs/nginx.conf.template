# Synapse自动部署系统 - Nginx配置模板
# 支持Well-known端点、ISP端口阻塞处理和多域名配置

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # HTTP重定向到HTTPS（基础域名）
    server {
        listen 80;
        server_name DOMAIN_NAME;
        
        # Well-known端点（HTTP访问）
        location /.well-known/matrix/server {
            return 200 '{"m.server": "MATRIX_DOMAIN:HTTPS_PORT"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        }

        location /.well-known/matrix/client {
            return 200 '{"m.homeserver": {"base_url": "https://MATRIX_DOMAIN:HTTPS_PORT"}, "m.identity_server": {"base_url": "https://vector.im"}, "org.matrix.msc3575.proxy": {"url": "https://MATRIX_DOMAIN:HTTPS_PORT"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        }

        # 其他请求重定向到HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS服务（基础域名）
    server {
        listen HTTPS_PORT ssl http2;
        server_name DOMAIN_NAME;

        ssl_certificate /etc/nginx/ssl/DOMAIN_NAME/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/DOMAIN_NAME/privkey.pem;

        # Well-known端点
        location /.well-known/matrix/server {
            return 200 '{"m.server": "MATRIX_DOMAIN:HTTPS_PORT"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        }

        location /.well-known/matrix/client {
            return 200 '{"m.homeserver": {"base_url": "https://MATRIX_DOMAIN:HTTPS_PORT"}, "m.identity_server": {"base_url": "https://vector.im"}, "org.matrix.msc3575.proxy": {"url": "https://MATRIX_DOMAIN:HTTPS_PORT"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        }

        # 根目录
        location / {
            root /usr/share/nginx/html;
            index index.html;
        }
    }

    # HTTP重定向到HTTPS（Matrix域名）
    server {
        listen 80;
        server_name MATRIX_DOMAIN;
        
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS服务（Matrix域名）
    server {
        listen HTTPS_PORT ssl http2;
        server_name MATRIX_DOMAIN;

        ssl_certificate /etc/nginx/ssl/MATRIX_DOMAIN/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/MATRIX_DOMAIN/privkey.pem;

        # 客户端API
        location ~ ^(/_matrix|/_synapse/client) {
            proxy_pass http://synapse:8008;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 缓冲设置
            proxy_buffering off;
            proxy_request_buffering off;
        }

        # 联邦API
        location ~ ^(/_matrix/federation) {
            proxy_pass http://synapse:8008;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            
            # 联邦专用设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Well-known端点
        location /.well-known/matrix/server {
            return 200 '{"m.server": "MATRIX_DOMAIN:HTTPS_PORT"}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }

        location /.well-known/matrix/client {
            return 200 '{"m.homeserver": {"base_url": "https://MATRIX_DOMAIN:HTTPS_PORT"}, "m.identity_server": {"base_url": "https://vector.im"}}';
            add_header Content-Type application/json;
            add_header Access-Control-Allow-Origin *;
        }

        # 根目录
        location / {
            return 404;
        }
    }
}
