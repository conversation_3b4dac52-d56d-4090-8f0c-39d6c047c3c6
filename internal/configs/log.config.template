# Synapse自动部署系统 - 日志配置模板
# 支持中文日志输出和结构化日志记录

version: 1

formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  chinese:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    datefmt: '%Y年%m月%d日 %H:%M:%S'

handlers:
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: chinese
    filename: /data/homeserver.log
    when: midnight
    interval: 1
    backupCount: 7
    encoding: utf8
    
  buffer:
    class: synapse.logging.handlers.PeriodicallyFlushingMemoryHandler
    target: file
    capacity: 10
    flushLevel: 30
    period: 5

  console:
    class: logging.StreamHandler
    formatter: chinese
    stream: ext://sys.stdout

loggers:
  synapse.storage.SQL:
    level: INFO
    
  synapse.access.http.8008:
    level: INFO
    
  synapse.federation.transport.server:
    level: INFO
    
  synapse.http.server:
    level: INFO
    
  synapse.storage.background_updates:
    level: INFO
    
  synapse.storage.engines:
    level: INFO
    
  synapse.handlers.auth:
    level: INFO
    
  synapse.handlers.federation:
    level: INFO
    
  synapse.handlers.room:
    level: INFO
    
  synapse.handlers.user_directory:
    level: INFO
    
  synapse.metrics:
    level: INFO
    
  synapse.util.caches:
    level: INFO

root:
  level: INFO
  handlers: [buffer]

disable_existing_loggers: false
