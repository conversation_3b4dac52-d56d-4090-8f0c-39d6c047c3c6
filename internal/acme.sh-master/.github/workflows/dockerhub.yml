
name: Build DockerHub
on:
  push:
    branches:
      - '*'
    tags:
      - '*'
    paths:
      - '**.sh'
      - "Dockerfile"
      - '.github/workflows/dockerhub.yml'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  DOCKER_IMAGE: neilpang/acme.sh

jobs:
  CheckToken:
    runs-on: ubuntu-latest
    outputs:
      hasToken: ${{ steps.step_one.outputs.hasToken }}
    env: 
      DOCKER_PASSWORD : ${{ secrets.DOCKER_PASSWORD }}
    steps:
      - name: Set the value
        id: step_one
        run: |
          if [ "$DOCKER_PASSWORD" ] ; then
            echo "hasToken=true" >>$GITHUB_OUTPUT
          else
            echo "hasToken=false" >>$GITHUB_OUTPUT
          fi
      - name: Check the value
        run: echo ${{ steps.step_one.outputs.hasToken }}
        
  build:
    runs-on: ubuntu-latest
    needs: CheckToken
    if: "contains(needs.CheckToken.outputs.hasToken, 'true')"
    steps:
      - name: checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          images: ${DOCKER_IMAGE}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: login to docker hub
        run: |
          echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin
      - name: build and push the image
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            DOCKER_IMAGE_TAG=${GITHUB_REF#refs/tags/}
          fi

          if [[ $GITHUB_REF == refs/heads/* ]]; then
            DOCKER_IMAGE_TAG=${GITHUB_REF#refs/heads/}

            if [[ $DOCKER_IMAGE_TAG == master ]]; then
              DOCKER_IMAGE_TAG=latest
              AUTO_UPGRADE=1
            fi
          fi

          DOCKER_LABELS=()
          while read -r label; do
            DOCKER_LABELS+=(--label "${label}")
          done <<<"${DOCKER_METADATA_OUTPUT_LABELS}"

          docker buildx build \
            --tag ${DOCKER_IMAGE}:${DOCKER_IMAGE_TAG} \
            "${DOCKER_LABELS[@]}" \
            --output "type=image,push=true" \
            --build-arg AUTO_UPGRADE=${AUTO_UPGRADE} \
            --platform linux/arm64/v8,linux/amd64,linux/arm/v6,linux/arm/v7,linux/386,linux/ppc64le,linux/s390x .
