name: Check dns api

on:
  pull_request_target:
    types:
      - opened
    paths:
      - 'dnsapi/*.sh'


jobs:
  welcome:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v6
        with:
          script: |
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `**Welcome**
                    READ ME !!!!!
                    Read me !!!!!!
                    First thing: don't send PR to the master branch, please send to the dev branch instead.
                    Please read the [DNS API Dev Guide](../wiki/DNS-API-Dev-Guide).
                    You MUST pass the [DNS-API-Test](../wiki/DNS-API-Test).
                    Then reply on this message, otherwise, your code will not be reviewed or merged.
                    Please also make sure to add/update the usage here: https://github.com/acmesh-official/acme.sh/wiki/dnsapi2
                    注意: 必须通过了 [DNS-API-Test](../wiki/DNS-API-Test) 才会被 review. 无论是修改, 还是新加的 dns api, 都必须确保通过这个测试.
                `
            })

