name: PebbleStrict
on:
  push:
    branches:
      - '*'
    paths:
      - '*.sh'
      - '.github/workflows/PebbleStrict.yml'
  pull_request:
    branches:
      - dev
    paths:
      - '*.sh'
      - '.github/workflows/PebbleStrict.yml'


concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true



jobs:
  PebbleStrict:
    runs-on: ubuntu-latest
    env:
      TestingDomain: example.com
      TestingAltDomains: www.example.com
      TEST_ACME_Server: https://localhost:14000/dir
      HTTPS_INSECURE: 1
      Le_HTTPPort: 5002
      TEST_LOCAL: 1
      TEST_CA: "Pebble Intermediate CA"

    steps:
    - uses: actions/checkout@v4
    - name: Install tools
      run: sudo apt-get install -y socat
    - name: Run Pebble
      run: cd .. && curl https://raw.githubusercontent.com/letsencrypt/pebble/master/docker-compose.yml >docker-compose.yml && docker compose up -d
    - name: Set up Pebble
      run: curl --request POST --data '{"ip":"**********"}' http://localhost:8055/set-default-ipv4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - name: Run acmetest
      run: cd ../acmetest && ./letest.sh

  PebbleStrict_IPCert:
    runs-on: ubuntu-latest
    env:
      TestingDomain: **********
      TEST_ACME_Server: https://localhost:14000/dir
      HTTPS_INSECURE: 1
      Le_HTTPPort: 5002
      Le_TLSPort: 5001
      TEST_LOCAL: 1
      TEST_CA: "Pebble Intermediate CA"
      TEST_IPCERT: 1

    steps:
    - uses: actions/checkout@v4
    - name: Install tools
      run: sudo apt-get install -y socat
    - name: Run Pebble
      run: |
        docker run --rm -itd --name=pebble \
        -e PEBBLE_VA_ALWAYS_VALID=1 \
        -p 14000:14000 -p 15000:15000   letsencrypt/pebble:latest pebble -config /test/config/pebble-config.json -strict
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - name: Run acmetest
      run: cd ../acmetest && ./letest.sh