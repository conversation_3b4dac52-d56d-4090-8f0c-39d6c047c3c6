name: Linux
on:
  push:
    branches:
      - '*'
    paths:
      - '*.sh'
      - '.github/workflows/Linux.yml'

  pull_request:
    branches:
      - dev
    paths:
      - '*.sh'
      - '.github/workflows/Linux.yml'


concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true




jobs:
  Linux:
    strategy:
      matrix:
        os: ["ubuntu:latest", "debian:latest", "almalinux:latest", "fedora:latest", "opensuse/leap:latest", "alpine:latest", "oraclelinux:8", "kalilinux/kali", "archlinux:latest", "mageia", "gentoo/stage3"]
    runs-on: ubuntu-latest
    env:
      TEST_LOCAL: 1
      TEST_PREFERRED_CHAIN: (STAGING)
      TEST_ACME_Server: "LetsEncrypt.org_test"
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: |
          cd .. \
          && git clone --depth=1 https://github.com/acmesh-official/acmetest.git \
          && cp -r acme.sh acmetest/
    - name: Run acmetest
      run: |
          cd ../acmetest \
          && ./rundocker.sh  testplat ${{ matrix.os }}



