name: Ubuntu
on:
  push:
    branches:
      - '*'
    paths:
      - '*.sh'
      - '.github/workflows/Ubuntu.yml'

  pull_request:
    branches:
      - dev
    paths:
      - '*.sh'
      - '.github/workflows/Ubuntu.yml'

concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true



jobs:
  Ubuntu:
    strategy:
      matrix:
        include:
         - TEST_ACME_Server: "LetsEncrypt.org_test"
           CA_ECDSA: ""
           CA: ""
           CA_EMAIL: ""
           TEST_PREFERRED_CHAIN: (STAGING)
         - TEST_ACME_Server: "LetsEncrypt.org_test"
           CA_ECDSA: ""
           CA: ""
           CA_EMAIL: ""
           TEST_PREFERRED_CHAIN: (STAGING)
           ACME_USE_WGET: 1
         - TEST_ACME_Server: "ZeroSSL.com"
           CA_ECDSA: "ZeroSSL ECC Domain Secure Site CA"
           CA: "ZeroSSL RSA Domain Secure Site CA"
           CA_EMAIL: "<EMAIL>"
           TEST_PREFERRED_CHAIN: ""
         - TEST_ACME_Server: "https://localhost:9000/acme/acme/directory"
           CA_ECDSA: "Smallstep Intermediate CA"
           CA: "Smallstep Intermediate CA"
           CA_EMAIL: ""
           TEST_PREFERRED_CHAIN: ""
           NO_REVOKE: 1
         - TEST_ACME_Server: "https://localhost:9000/acme/acme/directory"
           CA_ECDSA: "Smallstep Intermediate CA"
           CA: "Smallstep Intermediate CA"
           CA_EMAIL: ""
           TEST_PREFERRED_CHAIN: ""
           NO_REVOKE: 1
           TEST_IPCERT: 1
           TestingDomain: "**********"

    runs-on: ubuntu-latest
    env:
      TEST_LOCAL: 1
      TEST_ACME_Server: ${{ matrix.TEST_ACME_Server }}
      CA_ECDSA: ${{ matrix.CA_ECDSA }}
      CA: ${{ matrix.CA }}
      CA_EMAIL: ${{ matrix.CA_EMAIL }}
      NO_ECC_384: ${{ matrix.NO_ECC_384 }}
      TEST_PREFERRED_CHAIN: ${{ matrix.TEST_PREFERRED_CHAIN }}
      NO_REVOKE: ${{ matrix.NO_REVOKE }}
      TEST_IPCERT: ${{ matrix.TEST_IPCERT }}
      TestingDomain: ${{ matrix.TestingDomain }}
      ACME_USE_WGET: ${{ matrix.ACME_USE_WGET }}
    steps:
    - uses: actions/checkout@v4
    - name: Install tools
      run: sudo apt-get install -y socat wget
    - name: Start StepCA
      if: ${{ matrix.TEST_ACME_Server=='https://localhost:9000/acme/acme/directory' }}
      run: |
           docker run --rm -d \
            -p 9000:9000 \
            -e "DOCKER_STEPCA_INIT_NAME=Smallstep" \
            -e "DOCKER_STEPCA_INIT_DNS_NAMES=localhost,$(hostname -f)" \
            -e "DOCKER_STEPCA_INIT_REMOTE_MANAGEMENT=true" \
            -e "DOCKER_STEPCA_INIT_PASSWORD=test" \
            --name stepca \
            smallstep/step-ca:0.23.1

            sleep 5
            docker exec  stepca bash -c "echo test >test" \
            && docker exec stepca step ca provisioner add acme --type ACME --admin-subject  step --admin-password-file=/home/<USER>/test \
            && docker exec  stepca kill -1 1 \
            && docker exec  stepca cat /home/<USER>/certs/root_ca.crt | sudo bash -c "cat - >>/etc/ssl/certs/ca-certificates.crt"
    - name: Clone acmetest
      run: |
          cd .. \
          && git clone --depth=1 https://github.com/acmesh-official/acmetest.git \
          && cp -r acme.sh acmetest/
    - name: Run acmetest
      run: |
          cd ../acmetest \
          && sudo --preserve-env ./letest.sh


