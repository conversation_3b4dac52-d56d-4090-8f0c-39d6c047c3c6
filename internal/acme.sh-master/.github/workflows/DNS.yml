name: DNS
on:
  workflow_dispatch:
  push:
    paths:
      - 'dnsapi/*.sh'
      - '.github/workflows/DNS.yml'
  pull_request:
    branches:
      - 'dev'
    paths:
      - 'dnsapi/*.sh'
      - '.github/workflows/DNS.yml'

concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  CheckToken:
    runs-on: ubuntu-latest
    outputs:
      hasToken: ${{ steps.step_one.outputs.hasToken }}
    steps:
      - name: Set the value
        id: step_one
        run: |
          if [ "${{secrets.TokenName1}}" ] ; then
            echo "::set-output name=hasToken::true"
          else
            echo "::set-output name=hasToken::false"
          fi
      - name: Check the value
        run: echo ${{ steps.step_one.outputs.hasToken }}

  Fail:
    runs-on: ubuntu-latest
    needs: CheckToken
    if: "contains(needs.CheckToken.outputs.hasToken, 'false')"
    steps:
    - name: "Read this:   https://github.com/acmesh-official/acme.sh/wiki/DNS-API-Test"
      run: |
        echo "Read this:   https://github.com/acmesh-official/acme.sh/wiki/DNS-API-Test"
        if [ "${{github.repository_owner}}" != "acmesh-official" ]; then
          false
        fi

  Docker:
    runs-on: ubuntu-latest
    needs: CheckToken
    if: "contains(needs.CheckToken.outputs.hasToken, 'true')"
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - name: Set env file
      run: |
        cd ../acmetest
        if [ "${{ secrets.TokenName1}}" ] ; then
          echo "${{ secrets.TokenName1}}=${{ secrets.TokenValue1}}" >> docker.env
        fi
        if [ "${{ secrets.TokenName2}}" ] ; then
          echo "${{ secrets.TokenName2}}=${{ secrets.TokenValue2}}" >> docker.env
        fi
        if [ "${{ secrets.TokenName3}}" ] ; then
          echo "${{ secrets.TokenName3}}=${{ secrets.TokenValue3}}" >> docker.env
        fi
        if [ "${{ secrets.TokenName4}}" ] ; then
          echo "${{ secrets.TokenName4}}=${{ secrets.TokenValue4}}" >> docker.env
        fi
        if [ "${{ secrets.TokenName5}}" ] ; then
          echo "${{ secrets.TokenName5}}=${{ secrets.TokenValue5}}" >> docker.env
        fi

    - name: Run acmetest
      run: cd ../acmetest && ./rundocker.sh  testall




  MacOS:
    runs-on: macos-latest
    needs: Docker
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Install tools
      run:  brew install socat
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - name: Run acmetest
      run: |
        if [ "${{ secrets.TokenName1}}" ] ; then
          export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
        fi
        if [ "${{ secrets.TokenName2}}" ] ; then
          export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
        fi
        if [ "${{ secrets.TokenName3}}" ] ; then
          export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
        fi
        if [ "${{ secrets.TokenName4}}" ] ; then
          export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
        fi
        if [ "${{ secrets.TokenName5}}" ] ; then
          export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
        fi
        cd ../acmetest
        ./letest.sh




  Windows:
    runs-on: windows-latest
    needs: MacOS
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - name: Set git to use LF
      run: |
          git config --global core.autocrlf false
    - uses: actions/checkout@v4
    - name: Install cygwin base packages with chocolatey
      run: |
          choco config get cacheLocation
          choco install --no-progress cygwin
      shell: cmd
    - name: Install cygwin additional packages
      run: |
          C:\tools\cygwin\cygwinsetup.exe -qgnNdO -R C:/tools/cygwin -s https://mirrors.kernel.org/sourceware/cygwin/ -P socat,curl,cron,unzip,git
      shell: cmd
    - name: Set ENV
      shell: cmd
      run: |
          echo PATH=C:\tools\cygwin\bin;C:\tools\cygwin\usr\bin >> %GITHUB_ENV%
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - name: Run acmetest
      shell: bash
      run: |
        if [ "${{ secrets.TokenName1}}" ] ; then
          export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
        fi
        if [ "${{ secrets.TokenName2}}" ] ; then
          export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
        fi
        if [ "${{ secrets.TokenName3}}" ] ; then
          export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
        fi
        if [ "${{ secrets.TokenName4}}" ] ; then
          export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
        fi
        if [ "${{ secrets.TokenName5}}" ] ; then
          export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
        fi
        cd ../acmetest
        ./letest.sh



  FreeBSD:
    runs-on: ubuntu-latest
    needs: Windows
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/freebsd-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        prepare: pkg install -y socat curl
        usesh: true
        copyback: false
        run: |
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh




  OpenBSD:
    runs-on: ubuntu-latest
    needs: FreeBSD
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/openbsd-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        prepare: pkg_add socat curl libiconv
        usesh: true
        copyback: false
        run: |
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh




  NetBSD:
    runs-on: ubuntu-latest
    needs: OpenBSD
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/netbsd-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        prepare: |
          /usr/sbin/pkg_add curl socat
        usesh: true
        copyback: false
        run: |
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh




  DragonFlyBSD:
    runs-on: ubuntu-latest
    needs: NetBSD
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/dragonflybsd-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        prepare: |
          pkg install -y curl socat libnghttp2
        usesh: true
        copyback: false
        run: |
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh







  Solaris:
    runs-on: ubuntu-latest
    needs: DragonFlyBSD
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      HTTPS_INSECURE: 1 # always set to 1 to ignore https error, since Solaris doesn't accept the expired ISRG X1 root
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/solaris-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy HTTPS_INSECURE TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        copyback: false
        prepare: pkgutil -y -i socat
        run: |
          pkg set-mediator -v -I default@1.1 openssl
          export PATH=/usr/gnu/bin:$PATH
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh


  Omnios:
    runs-on: ubuntu-latest
    needs: Solaris
    env:
      TEST_DNS : ${{ secrets.TEST_DNS }}
      TestingDomain: ${{ secrets.TestingDomain }}
      TEST_DNS_NO_WILDCARD: ${{ secrets.TEST_DNS_NO_WILDCARD }}
      TEST_DNS_NO_SUBDOMAIN: ${{ secrets.TEST_DNS_NO_SUBDOMAIN }}
      TEST_DNS_SLEEP: ${{ secrets.TEST_DNS_SLEEP }}
      CASE: le_test_dnsapi
      TEST_LOCAL: 1
      DEBUG: ${{ secrets.DEBUG }}
      http_proxy: ${{ secrets.http_proxy }}
      https_proxy: ${{ secrets.https_proxy }}
      HTTPS_INSECURE: 1 # always set to 1 to ignore https error, since Omnios doesn't accept the expired ISRG X1 root
      TokenName1: ${{ secrets.TokenName1}}
      TokenName2: ${{ secrets.TokenName2}}
      TokenName3: ${{ secrets.TokenName3}}
      TokenName4: ${{ secrets.TokenName4}}
      TokenName5: ${{ secrets.TokenName5}}
    steps:
    - uses: actions/checkout@v4
    - name: Clone acmetest
      run: cd .. && git clone --depth=1 https://github.com/acmesh-official/acmetest.git  && cp -r acme.sh acmetest/
    - uses: vmactions/omnios-vm@v1
      with:
        envs: 'TEST_DNS TestingDomain TEST_DNS_NO_WILDCARD TEST_DNS_NO_SUBDOMAIN TEST_DNS_SLEEP CASE TEST_LOCAL DEBUG http_proxy https_proxy HTTPS_INSECURE TokenName1 TokenName2 TokenName3 TokenName4 TokenName5 ${{ secrets.TokenName1}} ${{ secrets.TokenName2}} ${{ secrets.TokenName3}} ${{ secrets.TokenName4}} ${{ secrets.TokenName5}}'
        copyback: false
        prepare: pkg install socat
        run: |
          if [ "${{ secrets.TokenName1}}" ] ; then
            export ${{ secrets.TokenName1}}="${{ secrets.TokenValue1}}"
          fi
          if [ "${{ secrets.TokenName2}}" ] ; then
            export ${{ secrets.TokenName2}}="${{ secrets.TokenValue2}}"
          fi
          if [ "${{ secrets.TokenName3}}" ] ; then
            export ${{ secrets.TokenName3}}="${{ secrets.TokenValue3}}"
          fi
          if [ "${{ secrets.TokenName4}}" ] ; then
            export ${{ secrets.TokenName4}}="${{ secrets.TokenValue4}}"
          fi
          if [ "${{ secrets.TokenName5}}" ] ; then
            export ${{ secrets.TokenName5}}="${{ secrets.TokenValue5}}"
          fi
          cd ../acmetest
          ./letest.sh


