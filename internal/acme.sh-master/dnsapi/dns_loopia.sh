#!/usr/bin/env sh
# shellcheck disable=SC2034
dns_loopia_info='Loopia.se
Site: Loopia.se
Docs: github.com/acmesh-official/acme.sh/wiki/dnsapi#dns_loopia
Options:
 LOOPIA_Api API URL. E.g. "https://api.loopia.<TLD>/RPCSERV" where the <TLD> is one of: com, no, rs, se. Default: "se".
 LOOPIA_User Username
 LOOPIA_Password Password
'

LOOPIA_Api_Default="https://api.loopia.se/RPCSERV"

########  Public functions #####################

#Usage: add  _acme-challenge.www.domain.com   "XKrxpRBosdIKFzxW_CT3KLZNf6q0HG9i01zxXp5CPBs"
dns_loopia_add() {
  fulldomain=$1
  txtvalue=$2

  if ! _loopia_load_config; then
    return 1
  fi

  _loopia_save_config

  _debug "First detect the root zone"
  if ! _get_root "$fulldomain"; then
    _err "invalid domain"
    return 1
  fi
  _debug _sub_domain "$_sub_domain"
  _debug _domain "$_domain"

  _info "Adding record"

  if ! _loopia_add_sub_domain "$_domain" "$_sub_domain"; then
    return 1
  fi
  if ! _loopia_add_record "$_domain" "$_sub_domain" "$txtvalue"; then
    return 1
  fi

}

dns_loopia_rm() {
  fulldomain=$1
  txtvalue=$2

  if ! _loopia_load_config; then
    return 1
  fi

  _loopia_save_config

  _debug "First detect the root zone"
  if ! _get_root "$fulldomain"; then
    _err "invalid domain"
    return 1
  fi

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
    <methodName>removeSubdomain</methodName>
    <params>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
    </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password" "$_domain" "$_sub_domain")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"

  if ! _contains "$response" "OK"; then
    err_response=$(echo "$response" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    _err "Error could not get txt records: $err_response"
    return 1
  fi
}

####################  Private functions below ##################################

_loopia_load_config() {
  LOOPIA_Api="${LOOPIA_Api:-$(_readaccountconf_mutable LOOPIA_Api)}"
  LOOPIA_User="${LOOPIA_User:-$(_readaccountconf_mutable LOOPIA_User)}"
  LOOPIA_Password="${LOOPIA_Password:-$(_readaccountconf_mutable LOOPIA_Password)}"

  if [ -z "$LOOPIA_Api" ]; then
    LOOPIA_Api="$LOOPIA_Api_Default"
  fi

  if [ -z "$LOOPIA_User" ] || [ -z "$LOOPIA_Password" ]; then
    LOOPIA_User=""
    LOOPIA_Password=""

    _err "A valid Loopia API user and password not provided."
    _err "Please provide a valid API user and try again."

    return 1
  fi

  if _contains "$LOOPIA_Password" "'" || _contains "$LOOPIA_Password" '"'; then
    _err "Password contains a quotation mark or double quotation marks and this is not supported by dns_loopia.sh"
    return 1
  fi

  Encoded_Password=$(_xml_encode "$LOOPIA_Password")
  return 0
}

_loopia_save_config() {
  if [ "$LOOPIA_Api" != "$LOOPIA_Api_Default" ]; then
    _saveaccountconf_mutable LOOPIA_Api "$LOOPIA_Api"
  fi
  _saveaccountconf_mutable LOOPIA_User "$LOOPIA_User"
  _saveaccountconf_mutable LOOPIA_Password "$LOOPIA_Password"
}

_loopia_get_records() {
  domain=$1
  sub_domain=$2

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
    <methodName>getZoneRecords</methodName>
    <params>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
    </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password" "$domain" "$sub_domain")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"
  if ! _contains "$response" "<array>"; then
    err_response=$(echo "$response" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    _err "Error: $err_response"
    return 1
  fi
  return 0
}

_get_root() {
  domain=$1
  _debug "get root"

  domain=$1
  i=2
  p=1

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
  <methodName>getDomains</methodName>
  <params>
   <param>
    <value><string>%s</string></value>
   </param>
   <param>
    <value><string>%s</string></value>
   </param>
  </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"
  while true; do
    h=$(echo "$domain" | cut -d . -f "$i"-100)
    if [ -z "$h" ]; then
      #not valid
      return 1
    fi

    if _contains "$response" "$h"; then
      _sub_domain=$(printf "%s" "$domain" | cut -d . -f 1-"$p")
      _domain="$h"
      return 0
    fi
    p=$i
    i=$(_math "$i" + 1)
  done
  return 1

}

_loopia_add_record() {
  domain=$1
  sub_domain=$2
  txtval=$3

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
    <methodName>addZoneRecord</methodName>
    <params>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value>
          <struct>
            <member>
              <name>type</name>
              <value><string>TXT</string></value>
            </member>
            <member>
              <name>priority</name>
              <value><int>0</int></value>
            </member>
            <member>
              <name>ttl</name>
              <value><int>300</int></value>
            </member>
            <member>
              <name>rdata</name>
              <value><string>%s</string></value>
            </member>
          </struct>
        </value>
      </param>
    </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password" "$domain" "$sub_domain" "$txtval")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"

  if ! _contains "$response" "OK"; then
    err_response=$(echo "$response" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    _err "Error: $err_response"
    return 1
  fi
  return 0
}

_sub_domain_exists() {
  domain=$1
  sub_domain=$2

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
    <methodName>getSubdomains</methodName>
    <params>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
    </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password" "$domain")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"

  if _contains "$response" "$sub_domain"; then
    return 0
  fi
  return 1
}

_loopia_add_sub_domain() {
  domain=$1
  sub_domain=$2

  if _sub_domain_exists "$domain" "$sub_domain"; then
    return 0
  fi

  xml_content=$(printf '<?xml version="1.0" encoding="UTF-8"?>
  <methodCall>
    <methodName>addSubdomain</methodName>
    <params>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
      <param>
        <value><string>%s</string></value>
      </param>
    </params>
  </methodCall>' "$LOOPIA_User" "$Encoded_Password" "$domain" "$sub_domain")

  response="$(_post "$xml_content" "$LOOPIA_Api" "" "POST")"

  if ! _contains "$response" "OK"; then
    err_response=$(echo "$response" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    _err "Error: $err_response"
    return 1
  fi
  return 0
}

_xml_encode() {
  encoded_string=$1
  encoded_string=$(echo "$encoded_string" | sed 's/&/\&amp;/')
  encoded_string=$(echo "$encoded_string" | sed 's/</\&lt;/')
  encoded_string=$(echo "$encoded_string" | sed 's/>/\&gt;/')
  printf "%s" "$encoded_string"
}
