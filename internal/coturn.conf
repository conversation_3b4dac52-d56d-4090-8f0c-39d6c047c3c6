# Synapse自动部署系统 - Coturn配置模板
# 基于Coturn v4.6+，无web服务配置

# 基础配置
listening-port=3478
tls-listening-port=5349
listening-ip=0.0.0.0

# 外部IP配置（将被脚本自动替换）
external-ip=*************

# 中继端口范围（自定义范围）
min-port=65335
max-port=65535

# 认证配置
use-auth-secret
static-auth-secret=93e23a3b1c136899464fa7105560ca2d05164113c16149815cfe6109f5fb6211
realm=turn.example.com

# SSL证书配置
cert=/etc/nginx/ssl/turn.example.com/fullchain.pem
pkey=/etc/nginx/ssl/turn.example.com/privkey.pem

# 协议配置
fingerprint
lt-cred-mech

# 安全配置
no-multicast-peers
no-cli
no-tlsv1
no-tlsv1_1
no-stdout-log

# 日志配置
log-file=/var/log/coturn/coturn.log
verbose

# 性能配置
total-quota=100
user-quota=50
max-bps=1000000

# 数据库配置（使用内存数据库）
userdb=/var/lib/coturn/turndb

# 禁用不需要的功能
no-tcp-relay
denied-peer-ip=10.0.0.0-**************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=*********-***************

# 允许的对等IP范围
allowed-peer-ip=0.0.0.0-***************

# 进程配置
pidfile=/var/run/coturn/coturn.pid

# 用户配置
no-software-attribute
stale-nonce=600

# 移动端优化
mobility
no-udp-relay
