#!/bin/bash

# Synapse自动部署系统 - 智能配置检查和修复脚本
# 自动检查配置文件并提供修复建议

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.env"
EXAMPLE_FILE="$SCRIPT_DIR/config-example.env"

# 检查结果
CHECKS_PASSED=0
CHECKS_FAILED=0
ISSUES_FOUND=()

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_pass() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((CHECKS_PASSED++))
}

log_fail() {
    echo -e "${RED}[✗]${NC} $1"
    ((CHECKS_FAILED++))
    ISSUES_FOUND+=("$1")
}

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    智能配置检查和修复${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
}

# 检查配置文件是否存在
check_config_file_exists() {
    echo -e "${BLUE}检查配置文件...${NC}"
    
    if [[ -f "$CONFIG_FILE" ]]; then
        log_pass "配置文件存在: config.env"
        return 0
    else
        log_fail "配置文件不存在: config.env"
        
        if [[ -f "$EXAMPLE_FILE" ]]; then
            echo -e "${YELLOW}是否自动创建配置文件？${NC}"
            echo -e "${GREEN}[1]${NC} 是，创建默认配置文件"
            echo -e "${RED}[0]${NC} 否，手动处理"
            read -p "请选择 [0-1]: " choice
            
            if [[ "$choice" == "1" ]]; then
                cp "$EXAMPLE_FILE" "$CONFIG_FILE"
                chmod 600 "$CONFIG_FILE"
                log_info "已创建配置文件: $CONFIG_FILE"
                log_warn "请继续配置必需参数"
                return 0
            fi
        else
            log_error "配置示例文件也不存在: $EXAMPLE_FILE"
        fi
        return 1
    fi
}

# 检查必需配置项
check_required_config() {
    echo -e "\n${BLUE}检查必需配置项...${NC}"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_fail "无法检查配置项：配置文件不存在"
        return 1
    fi
    
    source "$CONFIG_FILE"
    
    # 必需配置项列表
    local required_configs=(
        "DOMAIN_NAME:基础域名"
        "CLOUDFLARE_API_TOKEN:Cloudflare API密钥"
        "CERT_EMAIL:证书通知邮箱"
        "ADMIN_USERNAME:管理员用户名"
        "ADMIN_PASSWORD:管理员密码"
    )
    
    for config_item in "${required_configs[@]}"; do
        local var_name="${config_item%:*}"
        local var_desc="${config_item#*:}"
        local var_value="${!var_name:-}"
        
        if [[ -n "$var_value" ]] && [[ "$var_value" != *"example"* ]] && [[ "$var_value" != *"your_"* ]]; then
            log_pass "$var_desc 已配置"
        else
            log_fail "$var_desc 未配置或使用默认值"
        fi
    done
}

# 检查域名配置
check_domain_config() {
    echo -e "\n${BLUE}检查域名配置...${NC}"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        return 1
    fi
    
    source "$CONFIG_FILE"
    
    # 检查域名格式
    if [[ -n "${DOMAIN_NAME:-}" ]] && [[ "$DOMAIN_NAME" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$ ]]; then
        log_pass "基础域名格式正确: $DOMAIN_NAME"
    else
        log_fail "基础域名格式错误或未设置"
    fi
    
    # 检查子域名配置
    local matrix_domain="${MATRIX_DOMAIN:-matrix.$DOMAIN_NAME}"
    local turn_domain="${TURN_DOMAIN:-turn.$DOMAIN_NAME}"
    
    if [[ "$matrix_domain" != "matrix.example.com" ]]; then
        log_pass "Matrix域名已配置: $matrix_domain"
    else
        log_fail "Matrix域名使用默认值"
    fi
    
    if [[ "$turn_domain" != "turn.example.com" ]]; then
        log_pass "TURN域名已配置: $turn_domain"
    else
        log_fail "TURN域名使用默认值"
    fi
}

# 检查API密钥配置
check_api_config() {
    echo -e "\n${BLUE}检查API配置...${NC}"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        return 1
    fi
    
    source "$CONFIG_FILE"
    
    # 检查Cloudflare API Token
    if [[ -n "${CLOUDFLARE_API_TOKEN:-}" ]] && [[ "$CLOUDFLARE_API_TOKEN" != "your_cloudflare_api_token_here" ]]; then
        # 简单验证Token格式（Cloudflare Token通常很长）
        if [[ ${#CLOUDFLARE_API_TOKEN} -gt 30 ]]; then
            log_pass "Cloudflare API Token已配置"
        else
            log_fail "Cloudflare API Token格式可能不正确"
        fi
    else
        log_fail "Cloudflare API Token未配置"
    fi
    
    # 检查邮箱格式
    if [[ -n "${CERT_EMAIL:-}" ]] && [[ "$CERT_EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        log_pass "证书邮箱格式正确: $CERT_EMAIL"
    else
        log_fail "证书邮箱格式错误或未设置"
    fi
}

# 提供配置建议
provide_config_suggestions() {
    echo -e "\n${BLUE}配置建议...${NC}"
    
    if [[ ${#ISSUES_FOUND[@]} -gt 0 ]]; then
        echo -e "${YELLOW}发现以下配置问题：${NC}"
        for issue in "${ISSUES_FOUND[@]}"; do
            echo -e "  • $issue"
        done
        
        echo -e "\n${CYAN}修复建议：${NC}"
        echo -e "1. 编辑配置文件："
        echo -e "   ${WHITE}nano $CONFIG_FILE${NC}"
        echo
        echo -e "2. 必需配置项："
        echo -e "   ${WHITE}DOMAIN_NAME${NC}=您的域名（如：example.com）"
        echo -e "   ${WHITE}CLOUDFLARE_API_TOKEN${NC}=您的Cloudflare API密钥"
        echo -e "   ${WHITE}CERT_EMAIL${NC}=您的邮箱地址"
        echo -e "   ${WHITE}ADMIN_USERNAME${NC}=管理员用户名"
        echo -e "   ${WHITE}ADMIN_PASSWORD${NC}=安全的管理员密码"
        echo
        echo -e "3. 获取Cloudflare API Token："
        echo -e "   访问：${WHITE}https://dash.cloudflare.com/profile/api-tokens${NC}"
        echo -e "   权限：Zone:Zone:Read, Zone:DNS:Edit"
        echo
        echo -e "4. 配置完成后重新运行此脚本验证"
    else
        echo -e "${GREEN}✓ 所有配置项检查通过！${NC}"
        echo -e "${CYAN}下一步操作：${NC}"
        echo -e "  运行部署：${WHITE}./install.sh${NC}"
        echo -e "  或手动部署：${WHITE}./deploy.sh${NC}"
    fi
}

# 生成检查报告
generate_report() {
    echo
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}      配置检查报告${NC}"
    echo -e "${CYAN}================================${NC}"
    
    local total_checks=$((CHECKS_PASSED + CHECKS_FAILED))
    local success_rate=0
    
    if [[ $total_checks -gt 0 ]]; then
        success_rate=$((CHECKS_PASSED * 100 / total_checks))
    fi
    
    echo -e "检查时间: $(date '+%Y年%m月%d日 %H:%M:%S')"
    echo -e "总检查项: $total_checks"
    echo -e "通过检查: ${GREEN}$CHECKS_PASSED${NC}"
    echo -e "失败检查: ${RED}$CHECKS_FAILED${NC}"
    echo -e "成功率: ${success_rate}%"
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo
        echo -e "${GREEN}🎉 配置检查完全通过！系统已准备好部署。${NC}"
        return 0
    else
        echo
        echo -e "${RED}❌ 发现配置问题，请根据上述建议进行修复。${NC}"
        return 1
    fi
}

# 主函数
main() {
    show_banner
    
    # 执行各项检查
    check_config_file_exists
    check_required_config
    check_domain_config
    check_api_config
    
    # 提供建议和生成报告
    provide_config_suggestions
    generate_report
}

# 执行主函数
main "$@"
