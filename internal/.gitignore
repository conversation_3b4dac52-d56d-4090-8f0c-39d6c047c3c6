# Synapse自动部署系统 - Git忽略规则

# 运行时生成的配置文件（从模板生成）
homeserver.yaml
nginx.conf
coturn.conf
log.config

# 用户配置文件
config.env

# SSL证书文件
ssl/
*.pem
*.crt
*.key
*.csr

# 运行时数据目录
data/
logs/
media_store/
uploads/

# 备份文件
backup/
*.backup
*.sql
*.tar.gz

# 运行时生成的文件
*.log
*.pid
*.sock
*.tmp

# Docker相关
.docker/
docker-compose.override.yml

# Python相关
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
routeros/venv/
routeros/env/
routeros/.venv/
routeros/.env/

# 临时文件
temp/
tmp/
*.swp
*.swo
*~

# RouterOS运行时配置
routeros/routeros_config.json
routeros/ip_history.json
routeros/*.log

# acme.sh证书目录（如果在项目内）
.acme.sh/

# 系统文件
.DS_Store
Thumbs.db

# 编辑器文件
.vscode/
.idea/
*.sublime-*
