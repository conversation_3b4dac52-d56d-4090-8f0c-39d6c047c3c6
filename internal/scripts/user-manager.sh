#!/bin/bash

# Synapse自动部署系统 - 用户管理脚本
# 实现Synapse用户创建、管理和权限设置功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INTERNAL_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 配置文件路径（优先使用internal目录内的配置）
if [[ -f "$INTERNAL_DIR/config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/config.env"
elif [[ -f "$INTERNAL_DIR/../config.env" ]]; then
    CONFIG_FILE="$INTERNAL_DIR/../config.env"
else
    CONFIG_FILE="$INTERNAL_DIR/config.env"
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
}

# 加载配置
load_config() {
    # 自动创建配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warn "配置文件不存在，正在创建默认配置文件..."

        if [[ -f "$INTERNAL_DIR/config-example.env" ]]; then
            cp "$INTERNAL_DIR/config-example.env" "$CONFIG_FILE"
            chmod 600 "$CONFIG_FILE"
            log_info "已创建配置文件: $CONFIG_FILE"
        fi
    fi

    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
    fi
    
    # 设置默认值
    MATRIX_DOMAIN="${MATRIX_DOMAIN:-matrix.example.com}"
    SYNAPSE_SERVER_NAME="${SYNAPSE_SERVER_NAME:-$MATRIX_DOMAIN}"
}

# 检查Synapse容器状态
check_synapse_container() {
    cd "$INTERNAL_DIR"
    
    if ! docker compose ps synapse | grep -q "Up"; then
        log_error "Synapse容器未运行，请先启动服务"
        log_info "启动命令: cd $INTERNAL_DIR && docker compose up -d"
        return 1
    fi
    
    return 0
}

# 显示用户管理菜单
show_user_menu() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 用户管理系统${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "${GREEN}服务器: $SYNAPSE_SERVER_NAME${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}[1]${NC} 👤 创建新用户"
    echo -e "${GREEN}[2]${NC} 🔑 重置用户密码"
    echo -e "${GREEN}[3]${NC} 👑 设置管理员权限"
    echo -e "${GREEN}[4]${NC} 📋 列出所有用户"
    echo -e "${GREEN}[5]${NC} 🚫 停用用户"
    echo -e "${GREEN}[6]${NC} ✅ 激活用户"
    echo -e "${GREEN}[7]${NC} 🗑️  删除用户"
    echo -e "${GREEN}[8]${NC} 📊 用户统计信息"
    echo -e "${RED}[0]${NC} 🔙 退出"
    echo
    echo -e "${CYAN}================================${NC}"
}

# 创建新用户
create_user() {
    log_step "创建新用户"
    
    echo -e "${BLUE}请输入用户信息:${NC}"
    
    # 获取用户名
    read -p "用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    # 验证用户名格式
    if [[ ! "$username" =~ ^[a-zA-Z0-9._-]+$ ]]; then
        log_error "用户名只能包含字母、数字、点、下划线和连字符"
        return 1
    fi
    
    # 获取密码
    read -s -p "密码: " password
    echo
    if [[ -z "$password" ]]; then
        log_error "密码不能为空"
        return 1
    fi
    
    # 确认密码
    read -s -p "确认密码: " password_confirm
    echo
    if [[ "$password" != "$password_confirm" ]]; then
        log_error "密码不匹配"
        return 1
    fi
    
    # 询问是否设为管理员
    echo -e "\n${YELLOW}是否设置为管理员？${NC}"
    echo -e "${RED}[0]${NC} 普通用户"
    echo -e "${GREEN}[1]${NC} 管理员"
    read -p "请选择 [0-1]: " is_admin
    
    local admin_flag=""
    if [[ "$is_admin" == "1" ]]; then
        admin_flag="-a"
        log_info "将创建管理员用户"
    else
        log_info "将创建普通用户"
    fi
    
    # 创建用户
    log_info "正在创建用户: $username"
    
    cd "$INTERNAL_DIR"
    if docker compose exec synapse register_new_matrix_user \
        -u "$username" \
        -p "$password" \
        $admin_flag \
        -c /data/homeserver.yaml \
        http://localhost:8008; then
        
        log_info "用户创建成功: @$username:$SYNAPSE_SERVER_NAME"
        
        if [[ "$is_admin" == "1" ]]; then
            log_info "管理员权限已设置"
        fi
    else
        log_error "用户创建失败"
        return 1
    fi
}

# 重置用户密码
reset_password() {
    log_step "重置用户密码"
    
    # 获取用户名
    read -p "请输入用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    # 获取新密码
    read -s -p "新密码: " new_password
    echo
    if [[ -z "$new_password" ]]; then
        log_error "密码不能为空"
        return 1
    fi
    
    # 确认密码
    read -s -p "确认新密码: " password_confirm
    echo
    if [[ "$new_password" != "$password_confirm" ]]; then
        log_error "密码不匹配"
        return 1
    fi
    
    # 重置密码
    log_info "正在重置密码..."
    
    cd "$INTERNAL_DIR"
    if docker compose exec synapse synapse_admin reset-password \
        "@$username:$SYNAPSE_SERVER_NAME" \
        "$new_password"; then
        
        log_info "密码重置成功: @$username:$SYNAPSE_SERVER_NAME"
    else
        log_error "密码重置失败"
        return 1
    fi
}

# 设置管理员权限
set_admin_privileges() {
    log_step "设置管理员权限"
    
    # 获取用户名
    read -p "请输入用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    # 询问操作类型
    echo -e "\n${YELLOW}请选择操作:${NC}"
    echo -e "${GREEN}[1]${NC} 授予管理员权限"
    echo -e "${RED}[2]${NC} 撤销管理员权限"
    read -p "请选择 [1-2]: " action
    
    local user_id="@$username:$SYNAPSE_SERVER_NAME"
    
    case "$action" in
        1)
            log_info "正在授予管理员权限..."
            cd "$INTERNAL_DIR"
            if docker compose exec synapse synapse_admin set-admin "$user_id" true; then
                log_info "管理员权限授予成功: $user_id"
            else
                log_error "管理员权限授予失败"
                return 1
            fi
            ;;
        2)
            log_info "正在撤销管理员权限..."
            cd "$INTERNAL_DIR"
            if docker compose exec synapse synapse_admin set-admin "$user_id" false; then
                log_info "管理员权限撤销成功: $user_id"
            else
                log_error "管理员权限撤销失败"
                return 1
            fi
            ;;
        *)
            log_error "无效选择"
            return 1
            ;;
    esac
}

# 列出所有用户
list_users() {
    log_step "用户列表"
    
    cd "$INTERNAL_DIR"
    
    log_info "正在获取用户列表..."
    
    # 使用Synapse管理API获取用户列表
    if docker compose exec synapse synapse_admin list-users; then
        log_info "用户列表获取完成"
    else
        log_warn "无法获取用户列表，尝试查询数据库..."
        
        # 备用方法：直接查询数据库
        docker compose exec synapse sqlite3 /data/homeserver.db \
            "SELECT name, admin, deactivated FROM users ORDER BY name;" 2>/dev/null || \
            log_error "无法获取用户信息"
    fi
}

# 停用用户
deactivate_user() {
    log_step "停用用户"
    
    # 获取用户名
    read -p "请输入要停用的用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    local user_id="@$username:$SYNAPSE_SERVER_NAME"
    
    # 确认操作
    echo -e "\n${RED}警告: 此操作将停用用户 $user_id${NC}"
    echo -e "${YELLOW}停用后用户将无法登录，但数据会保留${NC}"
    echo -e "${YELLOW}是否继续？${NC}"
    echo -e "${RED}[0]${NC} 取消"
    echo -e "${GREEN}[1]${NC} 确认停用"
    read -p "请选择 [0-1]: " confirm
    
    if [[ "$confirm" != "1" ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    # 停用用户
    log_info "正在停用用户: $user_id"
    
    cd "$INTERNAL_DIR"
    if docker compose exec synapse synapse_admin deactivate-user "$user_id"; then
        log_info "用户停用成功: $user_id"
    else
        log_error "用户停用失败"
        return 1
    fi
}

# 激活用户
activate_user() {
    log_step "激活用户"
    
    # 获取用户名
    read -p "请输入要激活的用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    local user_id="@$username:$SYNAPSE_SERVER_NAME"
    
    # 激活用户
    log_info "正在激活用户: $user_id"
    
    cd "$INTERNAL_DIR"
    if docker compose exec synapse synapse_admin activate-user "$user_id"; then
        log_info "用户激活成功: $user_id"
    else
        log_error "用户激活失败"
        return 1
    fi
}

# 删除用户
delete_user() {
    log_step "删除用户"
    
    # 获取用户名
    read -p "请输入要删除的用户名: " username
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    local user_id="@$username:$SYNAPSE_SERVER_NAME"
    
    # 严重警告
    echo -e "\n${RED}⚠️  严重警告 ⚠️${NC}"
    echo -e "${RED}此操作将永久删除用户 $user_id${NC}"
    echo -e "${RED}包括所有消息、房间和个人数据${NC}"
    echo -e "${RED}此操作不可撤销！${NC}"
    echo
    echo -e "${YELLOW}请输入用户名确认删除: $username${NC}"
    read -p "确认用户名: " confirm_username
    
    if [[ "$confirm_username" != "$username" ]]; then
        log_info "用户名不匹配，操作已取消"
        return 0
    fi
    
    echo -e "\n${YELLOW}最后确认，是否删除用户？${NC}"
    echo -e "${RED}[0]${NC} 取消"
    echo -e "${RED}[1]${NC} 确认删除"
    read -p "请选择 [0-1]: " final_confirm
    
    if [[ "$final_confirm" != "1" ]]; then
        log_info "操作已取消"
        return 0
    fi
    
    # 删除用户
    log_info "正在删除用户: $user_id"
    
    cd "$INTERNAL_DIR"
    if docker compose exec synapse synapse_admin delete-user "$user_id"; then
        log_info "用户删除成功: $user_id"
    else
        log_error "用户删除失败"
        return 1
    fi
}

# 显示用户统计信息
show_user_stats() {
    log_step "用户统计信息"
    
    cd "$INTERNAL_DIR"
    
    log_info "正在收集统计信息..."
    
    # 查询用户统计
    echo -e "\n${CYAN}用户统计:${NC}"
    
    # 总用户数
    local total_users
    total_users=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
    echo -e "  总用户数: ${WHITE}$total_users${NC}"
    
    # 活跃用户数
    local active_users
    active_users=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(*) FROM users WHERE deactivated = 0;" 2>/dev/null || echo "0")
    echo -e "  活跃用户: ${GREEN}$active_users${NC}"
    
    # 停用用户数
    local deactivated_users
    deactivated_users=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(*) FROM users WHERE deactivated = 1;" 2>/dev/null || echo "0")
    echo -e "  停用用户: ${RED}$deactivated_users${NC}"
    
    # 管理员用户数
    local admin_users
    admin_users=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(*) FROM users WHERE admin = 1;" 2>/dev/null || echo "0")
    echo -e "  管理员用户: ${YELLOW}$admin_users${NC}"
    
    # 房间统计
    echo -e "\n${CYAN}房间统计:${NC}"
    
    local total_rooms
    total_rooms=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(*) FROM rooms;" 2>/dev/null || echo "0")
    echo -e "  总房间数: ${WHITE}$total_rooms${NC}"
    
    # 最近活跃用户（7天内）
    echo -e "\n${CYAN}活跃度统计:${NC}"
    
    local recent_active
    recent_active=$(docker compose exec synapse sqlite3 /data/homeserver.db \
        "SELECT COUNT(DISTINCT user_id) FROM user_ips WHERE last_seen > (strftime('%s', 'now') - 7*24*3600) * 1000;" 2>/dev/null || echo "0")
    echo -e "  7天内活跃: ${GREEN}$recent_active${NC}"
    
    echo
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${WHITE}    Synapse 用户管理工具${NC}"
    echo -e "${CYAN}================================${NC}"
    echo
    echo -e "${GREEN}功能说明:${NC}"
    echo -e "  • 创建和管理 Matrix 用户"
    echo -e "  • 设置管理员权限"
    echo -e "  • 重置用户密码"
    echo -e "  • 停用/激活用户"
    echo -e "  • 查看用户统计信息"
    echo
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  • 需要 Synapse 容器正在运行"
    echo -e "  • 删除用户操作不可撤销"
    echo -e "  • 管理员权限谨慎授予"
    echo
    echo -e "${BLUE}用户名格式:${NC}"
    echo -e "  • 只能包含字母、数字、点、下划线和连字符"
    echo -e "  • 完整用户ID: @username:$SYNAPSE_SERVER_NAME"
    echo
}

# 主循环
main() {
    # 加载配置
    load_config
    
    # 检查Synapse容器
    if ! check_synapse_container; then
        exit 1
    fi
    
    while true; do
        show_user_menu
        read -p "请选择操作 [0-8]: " choice
        
        case $choice in
            1)
                create_user
                ;;
            2)
                reset_password
                ;;
            3)
                set_admin_privileges
                ;;
            4)
                list_users
                ;;
            5)
                deactivate_user
                ;;
            6)
                activate_user
                ;;
            7)
                delete_user
                ;;
            8)
                show_user_stats
                ;;
            0)
                echo -e "${GREEN}感谢使用用户管理系统！${NC}"
                exit 0
                ;;
            *)
                log_error "无效选择，请重试"
                ;;
        esac
        
        echo -e "\n${CYAN}按任意键继续...${NC}"
        read -n 1
    done
}

# 执行主函数
main "$@"
