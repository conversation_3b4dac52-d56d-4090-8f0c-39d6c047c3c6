# Synapse 自动部署系统 - 独立部署包

这是一个完整的Matrix Synapse自动部署系统独立部署包，包含了部署和管理Synapse服务器所需的所有文件。

## 🚀 快速开始

### 1. 环境准备
确保您的系统满足以下要求：
- Ubuntu 20.04+ 或 Debian 11+
- Docker Engine 24.0+ 和 Docker Compose V2
- 至少2GB内存和20GB存储空间
- 稳定的互联网连接

### 2. 智能配置
```bash
# 运行智能配置检查（推荐）
./config-check.sh

# 或手动配置
cp config-example.env config.env
nano config.env
```

### 3. 一键部署
```bash
# 运行安装向导（自动处理配置）
./install.sh

# 或直接运行部署脚本
./deploy.sh
```

## 📁 目录结构

```
internal/                              # 独立部署包根目录
├── README.md                          # 本文件
├── config-example.env                 # 配置示例文件
├── config-check.sh                    # 智能配置检查脚本
├── .gitignore                         # Git忽略规则
├── docker-compose.yml                 # Docker服务配置
├── deploy.sh                          # 主部署脚本
├── install.sh                         # 快速安装脚本
├── verify-package.sh                  # 包验证脚本
├── configs/                           # 配置模板目录
│   ├── homeserver.yaml.template       # Synapse配置模板
│   ├── nginx.conf.template            # Nginx配置模板
│   ├── coturn.conf.template           # Coturn配置模板
│   └── log.config.template            # 日志配置模板
├── scripts/                           # 管理脚本目录
│   ├── certificate-manager.sh         # 智能证书管理
│   ├── backup.sh                      # 系统备份
│   ├── health-check.sh               # 健康检查
│   └── user-manager.sh               # 用户管理
└── routeros/                          # RouterOS集成（可选）
    ├── ip-monitor.py                  # IP监控脚本
    └── requirements.txt               # Python依赖
```

## ⚙️ 配置说明

### 必需配置项
在 `config.env` 中设置以下必需参数：

```bash
# 基础域名配置
DOMAIN_NAME=your-domain.com
MATRIX_DOMAIN=matrix.your-domain.com
TURN_DOMAIN=turn.your-domain.com

# SSL证书配置
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CERT_EMAIL=<EMAIL>

# 管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password
```

### 可选配置项
- 端口配置（如果ISP阻塞标准端口）
- RouterOS集成设置
- 备份和监控配置
- 高级网络设置

## 🛠️ 管理功能

### 配置管理
```bash
# 智能配置检查和修复
./config-check.sh

# 手动编辑配置
nano config.env
```

### 证书管理
```bash
# 智能证书管理
./scripts/certificate-manager.sh smart

# 检查证书状态
./scripts/certificate-manager.sh status
```

### 系统维护
```bash
# 系统备份
./scripts/backup.sh backup

# 健康检查
./scripts/health-check.sh

# 用户管理
./scripts/user-manager.sh
```

### 服务管理
```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down
```

## 🔧 RouterOS集成（可选）

如果您使用RouterOS设备，可以启用动态IP监控：

```bash
# 1. 在config.env中启用RouterOS集成
ENABLE_ROUTEROS=true
ROUTEROS_HOST=***********
ROUTEROS_USERNAME=api_user
ROUTEROS_PASSWORD=your_password

# 2. 安装Python依赖
cd routeros
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 3. 启动IP监控
python3 ip-monitor.py
```

## 🆘 故障排除

### 常见问题
1. **Docker启动失败**: 检查Docker版本和权限
2. **证书申请失败**: 验证Cloudflare API Token和DNS配置
3. **服务无法访问**: 检查防火墙和端口配置

### 诊断命令
```bash
# 运行健康检查
./scripts/health-check.sh

# 检查Docker状态
docker compose ps
docker compose logs

# 检查证书状态
./scripts/certificate-manager.sh status
```

## 📋 系统要求

### 最低要求
- **CPU**: 1核心
- **内存**: 2GB
- **存储**: 20GB
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 2核心+
- **内存**: 4GB+
- **存储**: 50GB+
- **网络**: 高速互联网连接

## 🔒 安全注意事项

1. **保护配置文件**: config.env包含敏感信息，请妥善保管
2. **定期备份**: 使用备份脚本定期备份系统数据
3. **更新系统**: 定期更新Docker镜像和系统组件
4. **监控日志**: 定期检查系统日志和健康状态

## 📞 获取支持

如果遇到问题：
1. 运行健康检查脚本诊断问题
2. 查看Docker容器日志
3. 检查配置文件设置
4. 参考项目文档和故障排除指南

---

**注意**: 这是一个独立的部署包，包含了部署Matrix Synapse服务器所需的所有文件。请确保在部署前仔细阅读配置说明并正确设置所有必需参数。
