# Synapse自动部署系统 - 环境变量配置示例
# 复制此文件为 config.env 并根据您的环境修改配置

# ================================
# 基础域名配置
# ================================

# 基础域名（必填）
DOMAIN_NAME=example.com

# Matrix服务域名（默认：matrix.${DOMAIN_NAME}）
MATRIX_DOMAIN=matrix.${DOMAIN_NAME}

# TURN服务域名（默认：turn.${DOMAIN_NAME}）
TURN_DOMAIN=turn.${DOMAIN_NAME}

# ================================
# 网络和端口配置
# ================================

# HTTPS端口（默认：443）
# 如果ISP阻塞443端口，可以修改为其他端口如8443
HTTPS_PORT=443

# HTTP端口（默认：80）
# 如果ISP阻塞80端口，可以修改为其他端口如8080
HTTP_PORT=80

# Synapse内部端口（默认：8008）
SYNAPSE_PORT=8008

# TURN服务端口（默认：3478）
TURN_PORT=3478

# TURN UDP端口范围（默认：65335-65535）
TURN_UDP_MIN_PORT=65335
TURN_UDP_MAX_PORT=65535

# ================================
# SSL证书配置
# ================================

# Cloudflare API Token（用于DNS验证）
# 获取地址：https://dash.cloudflare.com/profile/api-tokens
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here

# 证书邮箱（Let's Encrypt通知邮箱）
CERT_EMAIL=acme@${DOMAIN_NAME}

# 证书有效期阈值（天数，默认30天）
# 剩余有效期超过此值时不会重新申请证书
CERT_THRESHOLD_DAYS=30

# ================================
# Synapse配置
# ================================

# Synapse服务器名称（通常与${DOMAIN_NAME}相同）
SYNAPSE_SERVER_NAME=${DOMAIN_NAME}

# 是否启用统计报告（默认：no）
SYNAPSE_REPORT_STATS=no

# 数据库类型（sqlite/postgresql，默认：postgresql）
DATABASE_TYPE=postgresql

# PostgreSQL配置（仅当DATABASE_TYPE=postgresql时需要）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=synapse
POSTGRES_USER=synapse
POSTGRES_PASSWORD=auto_generated

# ================================
# 管理员用户配置
# ================================

# 管理员用户名
ADMIN_USERNAME=admin

# 管理员密码（首次部署时设置）
ADMIN_PASSWORD=Matrix2025!

# ================================
# Synapse功能配置
# ================================

# 是否启用联邦（默认：true）
ENABLE_FEDERATION=true

# 是否启用用户注册（默认：true）
ENABLE_REGISTRATION=true

# 注册类型（默认：invite_only，可选：open, invite_only, disabled）
REGISTRATION_TYPE=invite_only

# 是否允许访客访问（默认：false）
ALLOW_GUEST_ACCESS=false

# 是否启用统计报告（默认：false）
ENABLE_STATS_REPORTING=false

# 最大上传文件大小（默认：50M）
MAX_UPLOAD_SIZE=50M

# ================================
# RouterOS集成配置（可选）
# ================================

# 是否启用RouterOS集成（true/false）
ENABLE_ROUTEROS=false

# RouterOS设备IP地址
ROUTEROS_HOST=***********

# RouterOS API用户名
ROUTEROS_USERNAME=api

# RouterOS API密码
ROUTEROS_PASSWORD=api

# RouterOS API端口（默认：8728）
ROUTEROS_PORT=8728

# 动态IP检查间隔（秒，默认：60）
IP_CHECK_INTERVAL=60

# RouterOS WAN接口名称（默认：WAN）
ROUTEROS_WAN_INTERFACE=WAN

# ================================
# 安全配置（可选）
# ================================

# 是否启用防火墙配置（true/false）
ENABLE_FIREWALL=false

# 是否启用fail2ban（true/false）
ENABLE_FAIL2BAN=false

# ================================
# 备份配置
# ================================

# 备份保留天数（默认：30天）
BACKUP_RETENTION_DAYS=30

# 备份存储路径（默认：./backup）
BACKUP_PATH=./backup

# 是否启用自动备份（true/false）
ENABLE_AUTO_BACKUP=true

# 备份间隔（小时，默认：24小时）
BACKUP_INTERVAL_HOURS=24

# ================================
# 高级配置
# ================================

# Docker镜像标签（默认：latest）
SYNAPSE_IMAGE_TAG=latest
COTURN_IMAGE_TAG=latest
NGINX_IMAGE_TAG=alpine

# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO

# 时区设置（默认：Asia/Shanghai）
TIMEZONE=Asia/Shanghai

# ================================
# 开发和调试配置
# ================================

# 是否启用调试模式（true/false）
DEBUG_MODE=false

# 是否保留临时文件（true/false）
KEEP_TEMP_FILES=false
REGISTRATION_SHARED_SECRET=ae7f52ceaad8225cd5899d54a0a08b9ed35ea2c3d10a8962aa5bc03aa3edd250
TURN_SHARED_SECRET=93e23a3b1c136899464fa7105560ca2d05164113c16149815cfe6109f5fb6211
