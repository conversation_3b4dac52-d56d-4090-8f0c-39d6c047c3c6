# Synapse自动部署系统 - Synapse配置模板
# 基于Synapse v1.110+，支持SQLite和PostgreSQL，支持中文日志

# 服务器配置
server_name: "example.com"
pid_file: /data/homeserver.pid
web_client_location: https://app.element.io/

# 网络配置
listeners:
  - port: 8008
    tls: false
    type: http
    x_forwarded: true
    bind_addresses: ['0.0.0.0']
    resources:
      - names: [client, federation]
        compress: false

# 数据库配置（动态生成）
database:
  name: psycopg2
  args:
    user: synapse
    password: auto_generated
    database: synapse
    host: localhost
    port: 5432
    cp_min: 5
    cp_max: 10

# 日志配置
log_config: "/data/log.config"

# 媒体存储
media_store_path: /data/media_store
uploads_path: /data/uploads

# 媒体配置
max_upload_size: 50M
max_image_pixels: 32M
dynamic_thumbnails: false

# URL预览
url_preview_enabled: true
url_preview_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# 注册配置
enable_registration: true
registration_shared_secret: "ae7f52ceaad8225cd5899d54a0a08b9ed35ea2c3d10a8962aa5bc03aa3edd250"

# 注册类型配置
# 如果invite_only为invite_only，则需要邀请码
registrations_require_3pid: []
allowed_local_3pids: []
enable_3pid_lookup: false

# 验证码配置（可选）
# enable_registration_captcha: false
# recaptcha_public_key: ""
# recaptcha_private_key: ""

# 用户目录
user_directory:
  enabled: true
  search_all_users: false
  prefer_local_users: true

# 统计报告
report_stats: false

# 签名密钥
signing_key_path: "/data/example.com.signing.key"

# 受信任的密钥服务器
trusted_key_servers:
  - server_name: "matrix.org"

# TURN服务器配置
turn_uris:
  - "turn:turn.example.com:3478?transport=udp"
  - "turn:turn.example.com:3478?transport=tcp"
  - "turns:turn.example.com:5349?transport=udp"
  - "turns:turn.example.com:5349?transport=tcp"

turn_shared_secret: "93e23a3b1c136899464fa7105560ca2d05164113c16149815cfe6109f5fb6211"
turn_user_lifetime: 1h
turn_allow_guests: true

# 联邦配置
federation_domain_whitelist: []

# 联邦启用开关
# 注意：在homeserver.yaml中，联邦通过监听器配置控制
# 如果true为false，将移除federation监听器
federation_ip_range_blacklist:
  - '*********/8'
  - '10.0.0.0/8'
  - '**********/12'
  - '***********/16'
  - '**********/10'
  - '***********/16'
  - '::1/128'
  - 'fe80::/64'
  - 'fc00::/7'

# 房间配置
encryption_enabled_by_default_for_room_type: all
enable_room_list_search: true

# 用户配置
allow_guest_access: false
default_room_version: "10"

# 速率限制
rc_message:
  per_second: 0.2
  burst_count: 10

rc_registration:
  per_second: 0.17
  burst_count: 3

rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

# 管理API
enable_media_repo: true
admin_contact: 'mailto:<EMAIL>'

# 安全配置
suppress_key_server_warning: true
serve_server_wellknown: false

# 应用服务
app_service_config_files: []

# 推送配置
push:
  include_content: true

# 实验性功能
experimental_features:
  # 启用新的用户目录搜索
  msc3827_enabled: true
  # 启用房间摘要API
  msc3266_enabled: true

# 缓存配置
caches:
  global_factor: 0.5
  per_cache_factors:
    get_users_who_share_room_with_user: 2.0

# 工作进程配置（单实例部署）
worker_app: synapse.app.homeserver
worker_log_config: /data/log.config

# 模块配置
modules: []

# 第三方ID服务器
trusted_third_party_id_servers:
  - matrix.org
  - vector.im

# 邮件配置（可选）
# email:
#   smtp_host: localhost
#   smtp_port: 25
#   smtp_user: ""
#   smtp_pass: ""
#   require_transport_security: false
# <AUTHOR> <EMAIL>"
#   app_name: Matrix
#   enable_notifs: false

# 密码策略
password_config:
  enabled: true
  policy:
    minimum_length: 8
    require_digit: true
    require_symbol: false
    require_lowercase: true
    require_uppercase: true

# 保留策略
retention:
  enabled: false
