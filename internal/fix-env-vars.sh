#!/bin/bash

# 修复环境变量问题的脚本
# 这个脚本会解析config.env中的变量引用并导出到环境中

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查配置文件是否存在
if [[ ! -f "$CONFIG_FILE" ]]; then
    log_error "配置文件不存在: $CONFIG_FILE"
    exit 1
fi

log_info "正在解析和导出环境变量..."

# 创建临时文件来存储解析后的变量
TEMP_ENV_FILE=$(mktemp)

# 第一步：读取所有基础变量（不包含变量引用的）
while IFS='=' read -r key value; do
    # 跳过注释和空行
    [[ $key =~ ^[[:space:]]*# ]] && continue
    [[ -z "$key" ]] && continue
    
    # 只处理有效的环境变量名
    if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
        
        # 如果值不包含变量引用，直接导出
        if [[ ! "$value" =~ \$\{.*\} ]]; then
            export "$key"="$value"
            echo "export $key=\"$value\"" >> "$TEMP_ENV_FILE"
            log_info "导出基础变量: $key=$value"
        fi
    fi
done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')

# 第二步：解析包含变量引用的变量
while IFS='=' read -r key value; do
    # 跳过注释和空行
    [[ $key =~ ^[[:space:]]*# ]] && continue
    [[ -z "$key" ]] && continue
    
    # 只处理有效的环境变量名
    if [[ $key =~ ^[A-Z_][A-Z0-9_]*$ ]] && [[ -n "$value" ]]; then
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']\|["'\'']$//g')
        
        # 如果值包含变量引用，进行替换
        if [[ "$value" =~ \$\{.*\} ]]; then
            # 使用envsubst进行变量替换
            resolved_value=$(echo "$value" | envsubst)
            export "$key"="$resolved_value"
            echo "export $key=\"$resolved_value\"" >> "$TEMP_ENV_FILE"
            log_info "导出解析变量: $key=$resolved_value"
        fi
    fi
done < <(grep -E '^[A-Z_][A-Z0-9_]*=' "$CONFIG_FILE" | grep -v '^#')

# 第三步：处理其他必需的变量
if [[ -z "${POSTGRES_PASSWORD:-}" ]] || [[ "${POSTGRES_PASSWORD:-}" == "auto_generated" ]]; then
    POSTGRES_PASSWORD=$(openssl rand -hex 16)
    export POSTGRES_PASSWORD="$POSTGRES_PASSWORD"
    echo "export POSTGRES_PASSWORD=\"$POSTGRES_PASSWORD\"" >> "$TEMP_ENV_FILE"
    log_info "生成数据库密码: POSTGRES_PASSWORD"
fi

if [[ -z "${REGISTRATION_SHARED_SECRET:-}" ]]; then
    REGISTRATION_SHARED_SECRET=$(openssl rand -hex 32)
    export REGISTRATION_SHARED_SECRET="$REGISTRATION_SHARED_SECRET"
    echo "export REGISTRATION_SHARED_SECRET=\"$REGISTRATION_SHARED_SECRET\"" >> "$TEMP_ENV_FILE"
    log_info "生成注册密钥: REGISTRATION_SHARED_SECRET"
fi

if [[ -z "${TURN_SHARED_SECRET:-}" ]]; then
    TURN_SHARED_SECRET=$(openssl rand -hex 32)
    export TURN_SHARED_SECRET="$TURN_SHARED_SECRET"
    echo "export TURN_SHARED_SECRET=\"$TURN_SHARED_SECRET\"" >> "$TEMP_ENV_FILE"
    log_info "生成TURN密钥: TURN_SHARED_SECRET"
fi

# 保存环境变量到文件
cp "$TEMP_ENV_FILE" "$SCRIPT_DIR/.env"
rm "$TEMP_ENV_FILE"

log_info "环境变量已导出到 .env 文件"
log_info "现在可以重新启动Docker服务"

# 显示关键变量
echo
log_info "关键环境变量:"
echo "  DOMAIN_NAME: ${DOMAIN_NAME:-未设置}"
echo "  MATRIX_DOMAIN: ${MATRIX_DOMAIN:-未设置}"
echo "  TURN_DOMAIN: ${TURN_DOMAIN:-未设置}"
echo "  SYNAPSE_SERVER_NAME: ${SYNAPSE_SERVER_NAME:-未设置}"
